import React, { useEffect, useState } from "react";
import { Redirect, useParams } from "react-router-dom";
import { AuthStatus } from "../../reducers/auth/authReducer";
import { useSelector, shallowEqual } from "react-redux";
import useCurrentFacility from "../../hooks/useCurrentFacility";
import useAuthContext from "../../hooks/useAuthContext";
import ScheduledReportsService from "../../services/ScheduledReportsService";
import apiClient from "../../auth/apiClient";
import useHasPermissions from "../../hooks/useHasPermissions";
import { useStyles } from "./styles";
import clsx from "clsx";
import { FindEntity } from "../../state/slices/entities";
import { selectParentEntity } from "../../state/slices/CoreEntity";
import { useCoreEntityContext } from "../../hooks/useCoreEntitySlice";
import { ENTITY_TYPE } from "../../constants";

export default function GetReportRedirect() {
  const classes = useStyles();
  const { hasPermissions } = useHasPermissions();
  const { facilityID } = useCurrentFacility();
  const isAdmin = useSelector((state) => state?.user?.isAdmin);
  const { rrid } = useParams();
  const { authReducer } = useAuthContext();
  const [authState] = authReducer;
  const scheduledReportsService = new ScheduledReportsService(apiClient);
  const [errorMessage, setErrorMessage] = useState(null);
  const useCoreEntitySlice = useCoreEntityContext();
  const parentEntity = useSelector((state) => {
      if (useCoreEntitySlice) return selectParentEntity(facilityID)(state);

      const entity = FindEntity(state.entities?.EntityList ?? [], facilityID);
      return FindEntity(state.entities?.EntityList ?? [], entity.parententityid);
  }, shallowEqual);
  const facilityGroupID =
    parentEntity?.typeid === ENTITY_TYPE.FacilityGroup ? parentEntity?.entityid : null;

  useEffect(() => {
    const fetchUrl = async () => {
      try {
        const response = await scheduledReportsService.getReportJobbyRrid(rrid);
        if (!response.data) {
          console.error("ReportJob not found", response);
          setErrorMessage("Report not available.");
          return;
        }

        if (!isAdmin) {
          const jobData = JSON.parse(response?.data?.reportparams);
          const { facilitygroupid, facilityid } = jobData.parameters;

          if ((facilitygroupid != null && facilitygroupid !== facilityGroupID) ||
              (facilitygroupid == null && facilityid !== facilityID)) {
            console.error("User does not have permission", response);
            setErrorMessage("You do not have permission to download this report.");
            return;
          }

          if (!hasPermissions([jobData.reportname])) {
            console.error("User does not have permission", response);
            setErrorMessage("You do not have permission to download this report.");
            return;
          }
        }

        const urlResponse = await scheduledReportsService.getSignedDownloadURL(rrid);
        if (!urlResponse.data) {
          console.error("Signed download URL could not be fetched", urlResponse);
          setErrorMessage("Report not available.");
          return;
        }

        window.location.replace(urlResponse.data);
      } catch (err) {
        if (err.response?.status === 404) {
          console.error("Report not found", err);
          setErrorMessage("Report not available. It may have expired.");
        } else {
          console.error("Failed to get signed download URL", err);
          setErrorMessage("Failed to download the report.");
        }
      }
    };

    if (authState.authStatus === AuthStatus.AUTHENTICATED) {
      fetchUrl();
    }
  }, [
    authState.authStatus,
    rrid,
    facilityID,
    facilityGroupID
  ]);

  if (authState.authStatus !== AuthStatus.AUTHENTICATED) {
    return (
      <Redirect
        to={{
          pathname: "/login",
          state: { redirectTo: `/getreport/${rrid}` },
        }}
      />
    );
  }

  if (errorMessage) {
    return (
      <div className={clsx([classes.errormessage])} role="alert">
        {errorMessage}
      </div>
    );
  }

  return null;
}
