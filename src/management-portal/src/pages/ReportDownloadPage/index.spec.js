import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import GetReportRedirect from "./index";
import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import useAuthContext from "../../hooks/useAuthContext";
import useCurrentFacility from "../../hooks/useCurrentFacility";
import useHasPermissions from "../../hooks/useHasPermissions";
import ScheduledReportsService from "../../services/ScheduledReportsService";
import { selectParentEntity } from "../../state/slices/CoreEntity";
import { useCoreEntityContext } from "../../hooks/useCoreEntitySlice";
import { ENTITY_TYPE } from "../../constants";

jest.mock("react-redux", () => ({
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));

jest.mock("react-router-dom", () => {
  const actual = jest.requireActual("react-router-dom");
  return {
    ...actual,
    useParams: jest.fn(),
    Redirect: Object.assign(
      function MockRedirect({ to }) {
        return (
          <div>Redirecting to {to.pathname} (state: {to.state.redirectTo})</div>
        );
      },
      { displayName: "MockRedirect" }
    ),
  };
});

jest.mock("../../hooks/useCurrentFacility", () => jest.fn());
jest.mock("../../hooks/useHasPermissions", () => jest.fn());
jest.mock("../../hooks/useAuthContext", () => jest.fn());
jest.mock("../../services/ScheduledReportsService");
jest.mock("../../hooks/useCoreEntitySlice", () => ({
  useCoreEntityContext: jest.fn(),
}));
jest.mock("../../state/slices/CoreEntity", () => ({
  selectParentEntity: jest.fn(),
}));

describe("GetReportRedirect", () => {
  const mockReplace = jest.fn();
  const mockGetReportJobbyRrid = jest.fn();
  const mockGetSignedDownloadURL = jest.fn();

  let isAdmin = false;
  let facilityGroupId = "fac123";

  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(console, "error").mockImplementation(() => {});

    ScheduledReportsService.mockImplementation(() => ({
      getReportJobbyRrid: mockGetReportJobbyRrid,
      getSignedDownloadURL: mockGetSignedDownloadURL,
    }));

    Object.defineProperty(window, "location", {
      writable: true,
      value: { replace: mockReplace },
    });

    useCurrentFacility.mockReturnValue({ facilityID: "fac123" });
    useParams.mockReturnValue({ rrid: "123" });
    useAuthContext.mockReturnValue({ authReducer: [{ authStatus: "AUTHENTICATED" }] });
    useHasPermissions.mockReturnValue({ hasPermissions: () => true });
    useCoreEntityContext.mockReturnValue({ entityScope: { facilityGroupId } });
    selectParentEntity.mockReturnValue(() => ({ typeid: ENTITY_TYPE.FacilityGroup, entityid: "fac123" }));
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test("Redirects if not authenticated", () => {
    useAuthContext.mockReturnValue({ authReducer: [{ authStatus: "UNAUTHENTICATED" }] });

    render(<GetReportRedirect />);
    expect(screen.getByText(/Redirecting to \/login/i)).toBeInTheDocument();
  });

  test("Shows error if getReportJobbyRrid returns null", async () => {
    useSelector.mockImplementation((selectorFn) =>
      selectorFn({
        entityScope: { facilityGroupId },
        user: { isAdmin: false },
      })
    );
    mockGetReportJobbyRrid.mockResolvedValueOnce({ data: null });

    render(<GetReportRedirect />);
    await screen.findByRole("alert");

    expect(console.error).toHaveBeenCalledWith("ReportJob not found", { data: null });
    expect(screen.getByText(/Report not available/i)).toBeInTheDocument();
  });

  test("Skips facility check if admin", async () => {
    isAdmin = true;
    useSelector.mockImplementation((selectorFn) =>
      selectorFn({
        entityScope: { facilityGroupId },
        user: { isAdmin },
      })
    );

    mockGetReportJobbyRrid.mockResolvedValueOnce({
      data: {
        reportparams: JSON.stringify({
          reportname: "AnyReport",
          parameters: { facilityid: "abc" },
        }),
      },
    });
    mockGetSignedDownloadURL.mockResolvedValueOnce({ data: "http://mock.url" });

    render(<GetReportRedirect />);
    await waitFor(() => expect(mockReplace).toHaveBeenCalledWith("http://mock.url"));
  });

  test("Shows error when facilitygroupid does not match", async () => {
    useSelector.mockImplementation((selectorFn) =>
      selectorFn({
        entityScope: { facilityGroupId: "fac123" },
        user: { isAdmin: false },
      })
    );

    mockGetReportJobbyRrid.mockResolvedValueOnce({
      data: {
        reportparams: JSON.stringify({
          reportname: "XReport",
          parameters: { facilitygroupid: "wrong" },
        }),
      },
    });

    render(<GetReportRedirect />);
    await screen.findByRole("alert");

    expect(console.error).toHaveBeenCalledWith("User does not have permission", {
      data: {
        reportparams: expect.any(String),
      },
    });
    expect(screen.getByText(/You do not have permission/i)).toBeInTheDocument();
  });

  test("Shows error when hasPermissions returns false", async () => {
    useSelector.mockImplementation((selectorFn) =>
      selectorFn({
        entityScope: { facilityGroupId: "fac123" },
        user: { isAdmin: false },
      })
    );

    useHasPermissions.mockReturnValue({ hasPermissions: () => false });

    mockGetReportJobbyRrid.mockResolvedValueOnce({
      data: {
        reportparams: JSON.stringify({
          reportname: "XReport",
          parameters: { facilitygroupid: "fac123" },
        }),
      },
    });

    render(<GetReportRedirect />);
    await screen.findByRole("alert");
    expect(screen.getByText(/You do not have permission/i)).toBeInTheDocument();
  });

  test("Shows error if signed URL is null", async () => {
    useSelector.mockImplementation((selectorFn) =>
      selectorFn({
        entityScope: { facilityGroupId: "fac123" },
        user: { isAdmin: false },
      })
    );

    mockGetReportJobbyRrid.mockResolvedValueOnce({
      data: {
        reportparams: JSON.stringify({
          reportname: "X",
          parameters: { facilitygroupid: "fac123" },
        }),
      },
    });

    mockGetSignedDownloadURL.mockResolvedValueOnce({ data: null });

    render(<GetReportRedirect />);
    await screen.findByRole("alert");
    expect(screen.getByText(/Report not available/i)).toBeInTheDocument();
  });

  test("Handles 404 from getReportJobbyRrid", async () => {
    useSelector.mockImplementation((selectorFn) =>
      selectorFn({
        entityScope: { facilityGroupId: "fac123" },
        user: { isAdmin: false },
      })
    );

    mockGetReportJobbyRrid.mockRejectedValueOnce({
      response: { status: 404 },
    });

    render(<GetReportRedirect />);
    await screen.findByRole("alert");

    expect(console.error).toHaveBeenCalledWith("Report not found", {
      response: { status: 404 },
    });
    expect(screen.getByText(/Report not available/i)).toBeInTheDocument();
  });

  test("Handles general error from getSignedDownloadURL", async () => {
    useSelector.mockImplementation((selectorFn) =>
      selectorFn({
        entityScope: { facilityGroupId: "fac123" },
        user: { isAdmin: false },
      })
    );

    mockGetReportJobbyRrid.mockResolvedValueOnce({
      data: {
        reportparams: JSON.stringify({
          reportname: "X",
          parameters: { facilitygroupid: "fac123" },
        }),
      },
    });

    const error = new Error("network error");
    mockGetSignedDownloadURL.mockRejectedValueOnce(error);

    render(<GetReportRedirect />);
    await screen.findByRole("alert");

    expect(console.error).toHaveBeenCalledWith("Failed to get signed download URL", error);
    expect(screen.getByText(/Failed to download the report/i)).toBeInTheDocument();
  });

  test("Succeeds and calls window.location.replace on valid flow", async () => {
    isAdmin = false;
    facilityGroupId = "fac123";
    useSelector.mockImplementation((selectorFn) =>
      selectorFn({
        entityScope: { facilityGroupId },
        user: { isAdmin },
      })
    );

    useHasPermissions.mockReturnValue({ hasPermissions: () => true });

    mockGetReportJobbyRrid.mockResolvedValueOnce({
      data: {
        reportparams: JSON.stringify({
          reportname: "ReportA",
          parameters: { facilitygroupid: "fac123" },
        }),
      },
    });

    mockGetSignedDownloadURL.mockResolvedValueOnce({ data: "http://mock.url" });

    render(<GetReportRedirect />);
    await waitFor(() =>
      expect(window.location.replace).toHaveBeenCalledWith("http://mock.url")
    );
  });
});
