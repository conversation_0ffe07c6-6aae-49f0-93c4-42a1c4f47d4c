import React, { useEffect } from "react";
import useThemeContext from "../../../hooks/useThemeSwitch";
import { setRootContainerWidth } from "../../../reducers/layout/layoutReducer";
import { DeviceControlDesktop, DeviceControlMobile } from "../../../components/DeviceControl";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import { useTheme } from "@material-ui/core/styles";
import {EntitySelectedProvider} from "../../../components/DeviceControl/Hooks";
import {ListeningProvider} from "../../../hooks/useDeviceListener/useDeviceListenerContext";
import {useStyles} from "./DeviceControlV2Page.styles";

const DeviceControlV2Page = props => {
    const classes = useStyles();

    const { layoutReducer } = useThemeContext();
    const [, layoutDispatch] = layoutReducer;
    const theme = useTheme();

    const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

    useEffect(() => {
        layoutDispatch({ type: setRootContainerWidth, payload: false });
    }, [layoutDispatch]);

    return (
        <div className={classes.pageWrapper}>
            <EntitySelectedProvider>
                <ListeningProvider>
                    {isMobile ? <DeviceControlMobile/> : <DeviceControlDesktop/>}
                </ListeningProvider>
            </EntitySelectedProvider>
        </div>
    );
};

export default DeviceControlV2Page;