import { createEntityAdapter, createSelector, createSlice} from "@reduxjs/toolkit";
import apiClient from "../../../auth/apiClient";
import {buildErrors, BuildTree, findEntityForTree, findNearestFacilityFromEntity} from "./CoreEntityUtil";
import * as c from "../../../constants/";
import {
    changeNewFacilities,
    deleteEntity,
    fillEntityLevel,
    addNewEntities,
    addPermissionsToEntity,
    updateEntitySettings,
    deleteEntitySettings,
    updateEntity,
    onboardNewFacility,
    onboardValetArea,
    createNewEntity,
    setSetting,
    setSettings,
    fillAssociatedEntityLevel
} from "./CoreEntityThunks"
import isEqual from 'lodash/isEqual'; // or use deep equality check if needed
import isNewerSequence from "../../../utils/sequenceNumber/sequenceUtils";
import {setOrgContext, setOrgRoot} from "../OrgTree";

const entityAdapter = createEntityAdapter({
    selectId: (entity) => entity.entityid,
});

// Slice
const slice = createSlice({
    name: "coreEntities",
    initialState: entityAdapter.getInitialState({
        ContextID: null, //adding ContextID to the initial state
    }), // Initialize with adapter state
    reducers: {
        setEntities:(state, action) => {
            const { entityID, entities } = action.payload;

            state.ContextID = entityID;

            const updatedEntities = entities.map((entity) => ({
                ...entity,
                context: true, // Mark all loaded entities as part of the context
            }));

            entityAdapter.setAll(state, updatedEntities);
        },
        setContext: (state, action) => {
            const { contextID, entitiesToAdd } = action.payload;
            state.ContextID = contextID;

            Object.values(state.entities).forEach((entity) => {
                entity.context = false;
            });
            const updatedEntities = entitiesToAdd.map((entity) => ({
                ...entity,
                context: true,
            }));

            try {
                entityAdapter.upsertMany(state, updatedEntities);
            } catch (error) {
                console.error('Failed to update entities:', error);
            }
        },
        setEntityProperty:(state, action) => {
            const { entityId, property, value } = action.payload;
            const foundEntity = state.entities[entityId]

            if(foundEntity){
                if (property === 'state' && value?.SequenceNumber) {
                    const incomingSeq = value.SequenceNumber;
                    const existingState = foundEntity[property] ?? {};
                    const existingSeq = existingState.SequenceNumber;
                    if (incomingSeq && existingSeq && !isNewerSequence(incomingSeq, existingSeq)) {
                        return;
                    }
                }
                foundEntity[property] = value
            }
        },
        setEntityStateIfOffline: (state, action) => {
            const { entityid, value } = action.payload;
            const foundEntity = state.entities[entityid]

            if(foundEntity && !foundEntity.state){
                foundEntity.state = value;
            }
        },
        setEntitiesProperty: (state, action) => {
            const {entities} = action.payload;
            entities.map(e => {
                const {entityid, property, value} = e;
                const foundEntity = state.entities[entityid]
                if (foundEntity) {
                    foundEntity[property] = value;
                }
            })
        },
        setSetting: (state, action) => {
            const { entityid, settingName, settingValue } = action.payload;
            const foundEntity = state.entities[entityid]
            if (!foundEntity) return;
            const setting = foundEntity.settings?.find(
                (x) => x.name.toLowerCase() === settingName.toLowerCase()
            );
            if (!setting) return;
            setting.value = settingValue;
        },
        removeEntity: (state, action) => {
            const entityId = action.payload;
            entityAdapter.removeOne(state, entityId);
        },
        clearEntityState: (state, action) => {
            const { entityId } = action.payload
            const foundEntity = state.entities[entityId]
            if (!foundEntity) return;
            foundEntity.state = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(deleteEntity.fulfilled, (state, action) => {
                const { entityid } = action.payload;
                entityAdapter.removeOne(state,entityid);
            })
            .addCase(updateEntity.fulfilled, (state, action) => {
                if(!action.payload) return
                const updatedEntity = action.payload;

                const entityId = updatedEntity.entityid;

                if (!entityId) {
                    console.error("No valid entity ID found in the payload:", updatedEntity);
                    return;
                }

                const existingEntity = state.entities[entityId];

                existingEntity.name = action.payload.name;
                existingEntity.details = action.payload.details;
                existingEntity.settings = action.payload.settings;
            })
            .addCase(onboardNewFacility.fulfilled, (state, action) => {
                const parentEntityContext = state.entities[action.payload.parentEntityID]?.context;

                if(!action.payload.facilityID){
                    entityAdapter.upsertOne(state, action.payload)
                    return;
                }
                let facilityMetaData = {
                    entityid: action.payload.facilityID,
                    parententityid: action.payload.parentEntityID,
                    name: action.payload.name,
                    entitytype: c.ENTITY_TYPE.Facility,
                    children: [],
                    show: false,
                    context: parentEntityContext ?? false
                };

                entityAdapter.upsertOne(state, facilityMetaData)
            })
            .addCase(onboardValetArea.fulfilled, (state, action) => {
                const parentEntityContext = state.entities[action.payload.parentEntityID]?.context;

                if(!action.payload.valetAreaID){
                    entityAdapter.upsertOne(state, action.payload)
                    return;
                }
                let valetareaMetaData = {
                    entityid: action.payload.valetAreaID,
                    name: action.payload.name,
                    parententityid: action.payload.parentEntityID,
                    entitytype: c.ENTITY_TYPE.ValetArea,
                    children: [],
                    show: false, // what does this do?
                    context: parentEntityContext ?? false
                };
                entityAdapter.upsertOne(state,valetareaMetaData)
            })
            .addCase(createNewEntity.fulfilled, (state, action) => {
                const parentEntityContext = state.entities[action.payload.parententityid]?.context;

                const updatedPayload = {
                    ...action.payload,
                    context: parentEntityContext ?? false
                };
                entityAdapter.addOne(state,updatedPayload)
            })
            .addCase(updateEntitySettings.fulfilled, (state, action) => {
                // this isn't doing anything in both slices SO WHY DOES THIS EVEN EXIST!?!?
            })
            .addCase(deleteEntitySettings.fulfilled, (state, action) => {
                const { entityId, settingName, settingValue } = action.meta.arg;

                const entity = state.entities[entityId];
                if (entity && entity.settings) {
                    // Create an updated version of the entity with filtered settings
                    const updatedEntity = {
                        ...entity,
                    };

                    updatedEntity.settings = updatedEntity.settings.map((setting) => {
                        if (setting.name === settingName.replace("setting.", "")) {
                            setting.value = settingValue.toString();
                        }
                        return setting;
                    });

                    // Use upsertOne to update the entity in the normalized state
                    entityAdapter.upsertOne(state, updatedEntity);
                }
            })
            .addCase(addPermissionsToEntity.fulfilled, (state, action) => {
                entityAdapter.upsertOne(state,action.payload)
            })
            .addCase(changeNewFacilities.fulfilled, (state, action) => {
                const { contextID , contextPermissions , entitiesToAdd } = action.payload;
                if(!contextID) return;

                entitiesToAdd.forEach((entity) => {
                    if(entity.entityid === contextID){
                        entity.permissions =       [
                            ...contextPermissions[0].permissions?.map(
                                (permission) => permission.permissionName
                            ),
                            ...contextPermissions[0].groups
                                ?.map((group) => [...group.permissions])
                                .flat(),
                        ] ?? [];
                    }
                })

                Object.values(state.entities).forEach((entity) => {
                    entity.context = false;
                });

                entitiesToAdd.forEach((entity) => {
                    entity.context = true;
                });

                state.ContextID = contextID;

                const filteredEntities = Object.values(entitiesToAdd).filter(entity => entity !== undefined);
                entityAdapter.upsertMany(state,filteredEntities)

            })
            .addCase(addNewEntities.fulfilled, (state, action) => {
                const { newEntitiesToAdd } = action.payload;
                entityAdapter.upsertMany(state, newEntitiesToAdd)
            })
            .addCase(fillEntityLevel.fulfilled, (state, action) => {
                const { payload } = action;
                if(!payload){
                    return
                }

                let newEntities = payload.built.filter((entity) => !state.entities[entity.entityid]);

                entityAdapter.upsertMany(state,newEntities)

            })
            .addCase(fillAssociatedEntityLevel.fulfilled, (state, action) => {
                return
            })
            .addCase(setSettings.fulfilled, (state, action) => {
                const updatedSettings = action.payload;
                updatedSettings.forEach((entity) => {
                    if (entity?.entityid) {
                        entityAdapter.upsertOne(state, entity);
                    } else {
                        console.warn('Skipping entity without a valid entityid', entity);
                    }
                });
            })
            .addCase(setSetting.fulfilled, (state, action) => {
                entityAdapter.upsertOne(state, action.payload)
            })
    },
});

// globalSelectors
const entitySelectors = entityAdapter.getSelectors((state) => state.coreEntities);

export const {
    selectAll: selectAllEntities, //Retrieve all entities as an array
    selectById: selectEntityById, // Retrieve a single entity by its ID
    selectIds: selectEntityIds, // Retrieve all entity IDs as an array
} = entitySelectors;

//Get entity of contextID
export const selectContextEntity = createSelector(
    (state) => state.coreEntities,
    (entities) => {
        const contextEntity = entities.entities[entities.ContextID];

        if (!contextEntity) {
            return null; // Return null if the context entity is not found
        }

        // Recursive function to find all children and their descendants
        const findChildren = (parentId) => {
            const directChildren = Object.values(entities.entities)
                .filter((entity) => entity.parententityid === parentId)
                .map(({ state, ...rest }) => rest); // Exclude the `state` field

            return directChildren.map((child) => ({
                ...child,
                children: findChildren(child.entityid), // Recursively find children for each child
            }));
        };

        return {
            ...contextEntity,
            children: findChildren(entities.ContextID), // Recursively find all children and descendants
        };
    }
);

// Gets associated entities on the context entity and puts it in a flat array
export const selectContextEntityFlat = createSelector(
    (state) => state.coreEntities.ContextID,
    (state) => state.coreEntities.entities,
    (state) => state.coreEntities.ids,
    (contextID, entities, ids) => {
        if (!entities[contextID]) {
            return null; // Return null if the context entity is not found
        }

        // Use the stable ids array instead of Object.values
        const associatedEntities = ids
            .filter(id => entities[id].parententityid === contextID)
            .map(id => {
                const { entityid, parententityid, typeid, name } = entities[id];
                return { entityid, parententityid, typeid, name };
            });

        return {
            contextID,
            children: associatedEntities
        };
    }
);

// TODO: look at re rendering
export const selectContextEntityWithState = createSelector(
    (state) => state.coreEntities,
    (entities) => {
        const contextEntity = entities.entities[entities.ContextID];

        if (!contextEntity) {
            return null; // Return null if the context entity is not found
        }

        // Recursive function to find all children and their descendants
        const findChildren = (parentId) => {
            const directChildren = Object.values(entities.entities)
                .filter((entity) => entity.parententityid === parentId)

            return directChildren.map((child) => ({
                ...child,
                children: findChildren(child.entityid), // Recursively find children for each child
            }));
        };

        return {
            ...contextEntity,
            children: findChildren(entities.ContextID), // Recursively find all children and descendants
        };
    }
);



export const selectContextEntityPermissions = createSelector(
    (state) => state.coreEntities,
    (entities) => {
        return entities.entities[entities.ContextID]?.permissions
    }
);

export const selectContextEntityName = createSelector(
    (state) => state.coreEntities,
    (entities) => {
        return entities.entities[entities.ContextID]?.name
    }
);

//Replaces the selectAllFacilities function
export const selectAllFacilities = createSelector(
    selectAllEntities,
    (entities) => entities.filter((entity) => entity.type === "Facility")
)


//replaces the BuildTree Function
export const selectEntityTree = createSelector(
    (state) => state.coreEntities,
    (entities) => BuildTree(entities)
);

// Update to proper camelCase after deprecation
export const selectRawIDsWithScope = createSelector(
    selectAllEntities,
    (entities) => {
        return entities.map((entity) => ({
            entityid: entity.entityid,
            parententityid: entity.parententityid || null, // Return null if no parentEntityId
        }));
    }
);

//Only used for the Overview page DON'T USE THIS
export const selectEntityForTree = (entityId) => createSelector(
    (state) => state.coreEntities.entities,
    (entities) => findEntityForTree(entities, entityId)
);

export const selectAllScopedPermissions = createSelector(
    (state) => state.coreEntities, // Access the normalized state slice
    (coreEntities) => {
        const result = {};
        // Iterate through the IDs to access each entity
        coreEntities.ids.forEach((id) => {
            const entity = coreEntities.entities[id];
            if (entity && entity.permissions && entity.permissions.length > 0) {
                result[id] = entity.permissions
            }
        });
        return result;
    }
);

export const selectAllEntitySettings = createSelector(
    (state) => state.coreEntities, // Access the normalized state slice
    (coreEntities) => {
        const result = [];

        // Iterate through the IDs to access each entity
        coreEntities.ids.forEach((id) => {
            const entity = coreEntities.entities[id];
            if (entity && entity.settings && entity.settings.length > 0) {
                result.push({
                    entityID: id,
                    settings: entity.settings
                })
            }
        });

        return result;
    }
);

// @description Gets entities with no state field useful GetEntityTreeWithNoState
// if you need everything in context but don't want it to re-render everytime a DSD comes in
export const selectEntitiesWithNoState = createSelector(
    (state) => state.coreEntities, // Access normalized entities
    (entities) => {
        const result = {};
        for (const [key, entity] of Object.entries(entities.entities)) {
            result[key] = {
                entityId: entity.entityid,
                parentEntityId: entity.parententityid || null,
                typeId: entity.typeid,
            };
        }
        if (!selectEntitiesWithNoState.previousResult) {
            selectEntitiesWithNoState.previousResult = result;
            return result;
        }

        // Return the previous result if nothing has changed
        if (isEqual(result, selectEntitiesWithNoState.previousResult)) {
            return selectEntitiesWithNoState.previousResult;
        }

        selectEntitiesWithNoState.previousResult = result;
        return result;
    }
);


export const createNearestFacilityFromEntity = (entityId) =>
    createSelector(
        (state) => state.coreEntities, // Access the coreEntities slice
        ({entities}) => findNearestFacilityFromEntity(entities, entityId)
    );

//wrapper function for selectNearestFacilityGroup
export const selectNearestFacilityFromEntity = (entityId) => (state) => createNearestFacilityFromEntity(entityId)(state);

export const selectAllDeviceIds = createSelector(
    (state) => state.coreEntities, // Access the coreEntities slice
    ({ entities, ids }) => {
        return ids
            .map((id) => entities[id])
            .filter((entity) => entity?.typeid === c.ENTITY_TYPE.Device)
            .map((entity) => entity.entityid);
    }
);

export const makeParentEntitySelector = (entityId) =>
    createSelector(
        (state) => state.coreEntities, // Access the coreEntities slice
        ({ entities }) => {
            const parentEntityId = entities[entityId]?.parententityid;
            return entities[parentEntityId] ?? {};
        }
    );

export const selectParentEntity = (entityId) => (state) => makeParentEntitySelector(entityId)(state);

export const loadCoreEntities = (entityID, isAdmin = false) => async (dispatch) => {
    apiClient
        .get(`entities/${entityID}/tree`)
        .then((resp) => {
            dispatch(setOrgContext({rootEntities: resp.data }));//load the full entity tree into EntityList
            dispatch(setEntities({ entityID, entities: resp.data }));
        })
        .catch((err) => {
            console.error(
                "Encountered error while loading all entities from context:",
                err
            );
        })
        .finally(() => {
            //do nothing, getting IDs next
        });
};

export const {
    setEntities,
    setContext,
    setEntityProperty,
    setEntityStateIfOffline,
    setEntitiesProperty,
    removeEntity,
    clearEntityState
} = slice.actions;

export default slice.reducer;
