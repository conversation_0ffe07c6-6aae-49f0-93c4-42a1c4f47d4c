import {useEffect, useRef, useState} from "react";

/* State */
import {fillEntityLevel, setEntityProperty} from "../../state/slices/entities";
import { addToQueue } from "../../state/slices/callcenter";
import { useDispatch } from "react-redux";

/* Custom Hooks */
import useHubContext from "../useHubContext";
import useCurrentUser from "../useCurrentUser";
import useDeviceIDs from "../useDeviceIDs";
import useScopedPermissions from "../useScopedPermissions";
import { useSelector } from "react-redux";
import useCurrentFacility from "../useCurrentFacility";

/* Constants*/
import { DEVICE_STATE_READ_TOPIC, PORTAL_TRIGGER } from "../../constants";
import {useCoreEntityContext} from "../useCoreEntitySlice";
import {
  fillEntityLevel as coreFillEntityLevel,
  setEntityProperty as coreSetEntityProperty
} from "../../state/slices/CoreEntity";
/* Services */
import DeviceStateService from "../../services/DeviceStateService";
import apiClient from "../../auth/apiClient";

const statePendingCalls = [];
const queuedDevicesThatNeedLoadedState = [];

const loadedStates = {};

export default function useCallCenterActions() {
  const useCoreEntitySlice = useCoreEntityContext();
  const deviceStateService = new DeviceStateService(apiClient);

  const dispatch = useDispatch();
  const { portalHub } = useHubContext();
  const currentUser = useCurrentUser();
  const userID = useRef();
  const currentUserRef = useRef();
  currentUserRef.current = currentUser;
  userID.current = currentUser.UserID;
  const allDeviceIds = useDeviceIDs();
  const deviceIdRef = useRef();
  deviceIdRef.current = allDeviceIds;
  const scopedPermissions = useScopedPermissions();
  const scopedPermissionsRef = useRef();
  scopedPermissionsRef.current = scopedPermissions;
  const scopeAwareFacilityID = useSelector((state) => state.entityScope?.facilityGroupId);
  const [forceUpdate, setForceUpdate] = useState(0);
  const scopeAwareFacilityIDRef = useRef();
  scopeAwareFacilityIDRef.current = scopeAwareFacilityID;
  const setEntityStateLoaded = (entityId, loaded = true) => {
    loadedStates[entityId] = loaded;
    setForceUpdate(prev => prev + 1);
    setForceUpdate(prev => prev + 1);
  };
  const isEntityStateLoaded = (entityId) => loadedStates[entityId] || false;

  useEffect(() => {
    handleCallsAwaitingToJoinQueue();
    handleCallsInQueueThatNeedState();
  }, [scopedPermissions]);

  /* Responsible for adding calls to the queue and fetching their device state */
  const handleCallsAwaitingToJoinQueue = () => {
    const pendingClone = JSON.parse(JSON.stringify(statePendingCalls));
    pendingClone.forEach(async (call) => {
      const permissions = scopedPermissions[call.parentEntityID];
      console.log('permissions', permissions);
      if (!permissions) {
        setEntityStateLoaded(call.deviceID, true);
        return;
      }
      if (permissions.includes("callcenter.view")) {
        try {
          // Call retrieveState asynchronously
          const result = await deviceStateService.retrieveState(call.deviceID);

          if(result.data.Devices[0] && result.data.Devices[0].Connected) {
            dispatch(
                useCoreEntitySlice ?
                    coreSetEntityProperty({
                      entityId: call.deviceID,
                      property: "state",
                      value: result.data.Devices[0]?.Dsd ?? null,
                    })
                    :
                    setEntityProperty({
                      entityid: call.deviceID,
                      property: "state",
                      value: result.data.Devices[0]?.Dsd ?? null,
                    })
            );
          }
          setEntityStateLoaded(call.deviceID, true);
          dispatch(addToQueue(call));
          statePendingCalls.splice(
            statePendingCalls.findIndex(
              (stateCall) => stateCall.parentEntityID === call.parentEntityID
            ),
            1
          );
        } catch (error) {
          console.error(`Error retrieving state for device ${call.deviceID}:`, error);
        }
      }
    });
  };

  /* Responsible for fetching device state of already queued calls */
  const handleCallsInQueueThatNeedState = () => {
    const pendingClone = JSON.parse(
      JSON.stringify(queuedDevicesThatNeedLoadedState)
    );
    pendingClone.forEach(async (device) => {
      try {
        // Call retrieveState asynchronously
        const result = await deviceStateService.retrieveState(device.deviceID);

        if(result.data.Devices[0] && result.data.Devices[0].Connected) {
          dispatch(
              useCoreEntitySlice ?
                  coreSetEntityProperty({
                    entityId: device.deviceID,
                    property: "state",
                    value: result.data.Devices[0]?.Dsd ?? null,
                  })
                  :
                  setEntityProperty({
                    entityid: device.deviceID,
                    property: "state",
                    value: result.data.Devices[0]?.Dsd ?? null,
                  })
          );
        }
        queuedDevicesThatNeedLoadedState.splice(
          queuedDevicesThatNeedLoadedState.findIndex(
            (queuedDeviceID) => queuedDeviceID === device.deviceID
          ),
          1
        );
        setEntityStateLoaded(device.deviceID, true);
      } catch (error) {
        console.error(`Error retrieving state for device ${device.deviceID}:`, error);
      }
    });
  };

  const addCallToQueue = (call) => {
    if (!currentUserRef.current.isAdmin && deviceIdRef.current?.find((x) => x == call.deviceID) == undefined) {
      console.log(
        `device ${call.deviceID} doesn't exist in context for call center. Fetching state...`
      );
      // Device doesn't exist in store
      // Get tree using parentID
      statePendingCalls.push(call);
      Promise.resolve(dispatch(
        useCoreEntitySlice ?
          coreFillEntityLevel({
            entityID: call.parentEntityID,
            userID: userID.current,
            updateContext: true,
            isAdminPass: true
          })
          :
          fillEntityLevel({
            entityID: scopeAwareFacilityIDRef.current || call.parentEntityID,
            userID: userID.current,
          })
      )).then(async (result) => {
        try {
          // After entity level is filled, call deviceStateService.retrieveState asynchronously
          const stateResult = await deviceStateService.retrieveState(call.deviceID);

          if(stateResult.data.Devices[0] && stateResult.data.Devices[0].Connected) {
            dispatch(
                useCoreEntitySlice ?
                    coreSetEntityProperty({
                      entityId: call.deviceID,
                      property: "state",
                      value: stateResult.data.Devices[0]?.Dsd ?? null,
                    })
                    :
                    setEntityProperty({
                      entityid: call.deviceID,
                      property: "state",
                      value: stateResult.data.Devices[0]?.Dsd ?? null,
                    })
            );
          }
        } catch (error) {
          console.error(`Error retrieving state for device ${call.deviceID}:`, error);
        }
        setEntityStateLoaded(call.deviceID, true);

        return result;
      });
    } else {
      // Device exists in store
      // check permission for currentUser
      if(currentUserRef.current.isAdmin)
        dispatch(addToQueue(call));

      const permissions = scopedPermissionsRef.current[call.parentEntityID];
      if (permissions && permissions.includes("callcenter.view")) {
        // add call to queue
        dispatch(addToQueue(call));
      }
      setEntityStateLoaded(call.deviceID, true);
    }
  };

  /* Used to fetch the entity level for the given deviceID if it does not exist */
  const getDeviceDetails = async (deviceID, parentEntityID, currentUserID) => {
    if (deviceIdRef.current?.find((x) => x == deviceID) == undefined) {
      console.log(
        `device ${deviceID} doesn't exist in context for call center. Fetching state...`
      );
      // Device doesn't exist in store
      // Get tree using parentID
      queuedDevicesThatNeedLoadedState.push({
        deviceID,
        parentEntityID,
      });

      // Return the promise from dispatch so it can be properly awaited
      return Promise.resolve(dispatch(
        useCoreEntitySlice ?
          coreFillEntityLevel({
                entityID: deviceID,
                userID: currentUserID,
                updateContext: true,
                isAdminPass: true
          })
          :
          fillEntityLevel({
            entityID: parentEntityID,
            userID: currentUserID,
          })
      )).then(async (result) => {
        try {
          // After entity level is filled, call deviceStateService.retrieveState asynchronously
          const stateResult = await deviceStateService.retrieveState(deviceID);

          if(stateResult.data.Devices[0] && stateResult.data.Devices[0].Connected) {
            dispatch(
                useCoreEntitySlice ?
                    coreSetEntityProperty({
                      entityId: deviceID,
                      property: "state",
                      value: stateResult.data.Devices[0]?.Dsd ?? null,
                    })
                    :
                    setEntityProperty({
                      entityid: deviceID,
                      property: "state",
                      value: stateResult.data.Devices[0]?.Dsd ?? null,
                    })
            );
          }
        } catch (error) {
          console.error(`Error retrieving state for device ${deviceID}:`, error);
        }
        setEntityStateLoaded(deviceID, true);

        return result;
      });
    }
    setEntityStateLoaded(deviceID, true);
  };

  return {
    addCallToQueue,
    getDeviceDetails,
    isStateLoaded: isEntityStateLoaded,
  };
}
