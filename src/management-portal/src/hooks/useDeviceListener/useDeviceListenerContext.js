import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useRef,
  useCallback
} from "react";
import {useSelector} from "react-redux";
import {useCoreEntityContext} from "../useCoreEntitySlice";
import {selectContextEntity} from "../../state/slices/CoreEntity";
import {ENTITY_TYPE} from "../../constants";

const deviceListeningContext = createContext();

export const useListening = () => {
  return useContext(deviceListeningContext);
};

// TODO: fine for now but will need to updated at some point
export const ListeningProvider = ({ children }) => {
  const useCoreEntitySlice = useCoreEntityContext();
  const currentContext = useSelector((state) =>useCoreEntitySlice ? selectContextEntity(state) : state.entities.Context, (one, two) => JSON.stringify(one) === JSON.stringify(two));
  const [listeningEntities, setListeningEntities] = useState([]);
  const listeningEntitiesRef = useRef(listeningEntities);

  const callCenterQueue = useSelector((state) => state.callcenter.Queue);

  useEffect(() => {
    listeningEntitiesRef.current = listeningEntities;
  }, [listeningEntities]);

  useEffect(() => {
    const updatedEntities = callCenterQueue?.reduce((acc, call) => {
      if (call?.deviceID && !acc.includes(call.deviceID)) {
        acc.push(call.deviceID);
      }
      return acc;
    }, []);

    if (updatedEntities && updatedEntities.length > 0) {
      setListeningEntities((prev) => [
        ...new Set([...prev, ...updatedEntities]),
      ]);
    }
  }, [callCenterQueue]);

  useEffect(() => {
    const collectDeviceIds = (children, acc, visited) => {
      children.forEach((child) => {
        if (visited.has(child.entityid)) return;
        visited.add(child.entityid);

        if (child.typeid === ENTITY_TYPE.Device && child.entityid && !acc.includes(child.entityid)) {
          acc.push(child.entityid);
        }

        if (child.children && child.children.length > 0) {
          collectDeviceIds(child.children, acc, visited);
        }
      });
    };

    if (!currentContext?.children) return;

    const currentContextDeviceIds = [];
    const visited = new Set();
    collectDeviceIds(currentContext.children, currentContextDeviceIds, visited);

    // Only update the state if there are new device IDs to add
    setListeningEntities((prev) => {
      const newDeviceIds = currentContextDeviceIds.filter(id => !prev.includes(id));
      if (newDeviceIds.length > 0) {
        return [...new Set([...prev, ...newDeviceIds])];
      }
      return prev;
    });
  }, [currentContext]);

  const isBeingListened = useCallback((entityId) => {
    if (
      !listeningEntitiesRef.current ||
      !Array.isArray(listeningEntitiesRef.current)
    ) {
      return false;
    }

    return listeningEntitiesRef.current.includes(entityId);
  }, []);
  return (
    <deviceListeningContext.Provider
      value={{ listeningEntities, isBeingListened }}
    >
      {children}
    </deviceListeningContext.Provider>
  );
};
