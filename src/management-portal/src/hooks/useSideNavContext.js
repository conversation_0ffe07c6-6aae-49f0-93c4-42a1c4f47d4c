import React, { createContext, useContext, useState} from "react";


const SideNavContext = createContext({});


export const useSideNavContext = () => useContext(SideNavContext);

// context for side nav checking not being used but could be used
export const SideNavContextProvider = ({children}) => {
    const [isOpen, setIsOpen] = useState(true);

    const handleToggle = (toggle) => {
        setIsOpen(!isOpen);
    }

    return (
        <SideNavContext.Provider value={{isOpen, handleToggle}}>
            {children}
        </SideNavContext.Provider>
    )
}