import React from "react";
import { shallow } from "enzyme";
import { But<PERSON> } from "@material-ui/core";
import PropertyButton from "./index";
import PropertyMenu from "../PropertyMenu";
import EntityService from "../../services/EntityService";
import useAuthContext from "../../hooks/useAuthContext";
import useCurrentFacility from "../../hooks/useCurrentFacility";
import useHasPermissions from "../../hooks/useHasPermissions";
import { useEnqueueSnackbar } from "../../hooks/useEnqueueSnackbar";

let mockHistoryPush = jest.fn();
jest.mock("../../hooks/useEnqueueSnackbar");
jest.mock("../../hooks/useHasPermissions");
jest.mock("../../hooks/useAuthContext");
jest.mock("../../hooks/useCurrentFacility");
jest.mock("../../hooks/auditing/useFacilityChangeAuditing");
jest.mock("../../services/EntityService", () => {
  return jest.fn().mockImplementation(() => {
    return { getPropertiesByScopes: jest.fn().mockReturnValue({ data: {} }) };
  });
});
jest.mock("react-router-dom", () => ({
  useHistory: () => ({
    push: mockHistoryPush
  })
}));
jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useDispatch: jest.fn(),
  useSelector: jest.fn()
}));

let component;
let mockDispatch;
let mockCurrentFacility;

describe("Property Button Tests", () => {
  beforeEach(() => {
    useEnqueueSnackbar.mockReturnValue(jest.fn());
    mockDispatch = jest.fn();
    useAuthContext.mockReturnValue({
      authReducer: [
        {
          currentFacility: {
            facilityID: "1",
            name: "TestProperty"
          },
          properties: [
            {
              facilityID: "1",
              name: "TestProperty",
              parentID: 1,
              parentName: "Test Name"
            },
            {
              facilityID: "2",
              name: "TestProperty2",
              parentID: 1,
              parentName: "Test Name"
            }
          ]
        },
        mockDispatch
      ]
    });
    
    mockCurrentFacility = { facilityID: "1", facilityName: "TestProperty" };
    useCurrentFacility.mockImplementation(() => mockCurrentFacility);
    
    useHasPermissions.mockReturnValue({
      hasPermissions: jest.fn().mockReturnValue(true)
    });
    component = shallow(<PropertyButton />);
    EntityService.mockClear();
  });

  describe("Smoke Test", () => {
    it("should render without exploding", () => {
      expect(component.find(Button).exists()).toBe(true);
      expect(component.find(PropertyMenu).exists()).toBe(true);
    });
  });

  describe("Button", () => {
    it("should display the currently selected property as its content", () => {
      expect(component.find(Button).text()).toBe("TestProperty");
    });

    it("should update data-facility when useCurrentFacility is updated", () => {
      // Check initial value
      let button = component.find('[data-id="facility-modal-btn"]');
      expect(button.prop('data-facility')).toBe("TestProperty");

      // Check updated value
      mockCurrentFacility = { facilityID: "2", facilityName: "TestProperty2" };
      component.setProps({});
      button = component.find('[data-id="facility-modal-btn"]');
      expect(button.prop('data-facility')).toBe("TestProperty2");
    });
  });

  describe("Modal", () => {
    it("should be closed on initial render", () => {
      expect(component.find(PropertyMenu).props().isOpen).toBe(false);
    });

    it("should open modal when clicked", () => {
      component.find(Button).simulate("click", {
        currentTarget: { value: 8 },
        setAnchorEl: () => {}
      });
      expect(component.find(PropertyMenu).props().isOpen).toBe(true);
    });
  });
});
