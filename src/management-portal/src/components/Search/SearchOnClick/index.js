import React from "react";
import { <PERSON>rid, <PERSON><PERSON><PERSON>, Button } from "@material-ui/core";
import { useStyles } from "./styles";
import PropTypes from "prop-types";

const SearchOnClick = ({ searchTerm, setSearchTerm, onSearch, minChars, searchLabel, isLoading }) => {
    const classes = useStyles();

    const handleSearchClick = () => {
        onSearch(searchTerm);
    };

    return (
        <Grid container >
            <Grid item xs={12}>
                <TextField
                    fullWidth
                    label={searchLabel || "Search for..."}
                    variant="outlined"
                    value={searchTerm}
                    onChange={(event) => setSearchTerm(event.target.value)}
                    helperText={`minimum ${minChars} characters`}
                    inputProps={{ "aria-label": "Search Text Box" }}
                    InputLabelProps={{ shrink: !!searchTerm }}
                />
            </Grid>
            <Grid item xs={12}>
                <div className={classes.searchButton}>
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={handleSearchClick}
                        disabled={!searchTerm || searchTerm.length < minChars || isLoading}
                        aria-label="Search Button"

                    >
                        {isLoading ? "Searching..." : "Search"}
                    </Button>
                </div>
            </Grid>
        </Grid >
    );
};

SearchOnClick.propTypes = {
    searchTerm: PropTypes.string,
    setSearchTerm: PropTypes.func,
    onSearch: PropTypes.func,
    minChars: PropTypes.number,
    searchLabel: PropTypes.string,
    isLoading: PropTypes.bool,
};

export default SearchOnClick;