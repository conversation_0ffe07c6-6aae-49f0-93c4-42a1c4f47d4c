import { render, screen, fireEvent } from "@testing-library/react";
import SearchOnClick from "../SearchOnClick";
import React from "react";

describe("SearchOnClick Component", () => {
    afterEach(() => {
        jest.clearAllMocks();
    });
    test("displays correct helper text based on minChars prop", () => {
        render(<SearchOnClick searchTerm="" setSearchTerm={() => { }} onSearch={() => { }} minChars={3} />);
        expect(screen.getByText("minimum 3 characters")).toBeInTheDocument();
    });

    test("disables search button when input is below minChars", () => {
        render(<SearchOnClick searchTerm="ab" setSearchTerm={() => { }} onSearch={() => { }} minChars={3} />);

        expect(screen.getByRole("button", { name: "Search Button" })).toBeDisabled();
    });

    test("enables search button when input meets minChars", () => {
        render(<SearchOnClick searchTerm="abc" setSearchTerm={() => { }} onSearch={() => { }} minChars={3} />);

        expect(screen.getByRole("button", { name: "Search Button" })).not.toBeDisabled();
    });

    test("calls onSearch when minChars is met and button is clicked", () => {
        const onSearchMock = jest.fn();
        render(<SearchOnClick searchTerm="abc" setSearchTerm={() => { }} onSearch={onSearchMock} minChars={3} />);

        const button = screen.getByRole("button", { name: "Search Button" });
        expect(button).not.toBeDisabled();

        fireEvent.click(button);
        expect(onSearchMock).toHaveBeenCalledWith("abc");
    });
});