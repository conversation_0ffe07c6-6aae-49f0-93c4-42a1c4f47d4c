import React from "react";
import { shallow } from "enzyme";
import AddRoutineAction from "./index";
import {
  ROUTINE_TYPE,
  SYSTEM_EVENTS,
  TEXT_ACTION_EVENTS,
} from "../../../../constants/index";
import { render, fireEvent } from "@testing-library/react";
import { useFeatureFlag } from "../../../../hooks/useFeatureFlags";
import { mockFlags, resetLDMocks } from "jest-launchdarkly-mock";
import { createMuiTheme , ThemeProvider, lighten } from "@material-ui/core";

jest.mock("../../../../hooks/useFeatureFlags");

const MockTheme = ({children}) => {
	const theme = createMuiTheme({
	  palette: {
		amano: {
		  base: {
			secondary:{
				main: "rgb(0, 107, 166)",
				light : lighten("rgb(0, 107, 166)",0.2)
			}
		  }
		},
	  },
	});
	return React.createElement(ThemeProvider, {theme: theme}, children);
}

describe("AddRoutineAction", () => {
  let wrapper;
  beforeEach(() => {
    resetLDMocks();
    useFeatureFlag.mockReturnValue(true);
    mockFlags({ routinesInventoryArchive: false });
  });

  it("should render without blowing up", () => {
    wrapper = shallow(<MockTheme><AddRoutineAction /></MockTheme>);
    expect(wrapper.exists()).toBe(true);
  });

  it("should close drawer when close button is clicked", () => {
    let spy = jest.fn();

    const { getByText } = render(<MockTheme><AddRoutineAction handleClose={spy} /></MockTheme>);

    const closeBtn = getByText("Close");
    fireEvent.click(closeBtn);

    expect(spy).toHaveBeenCalledTimes(1);
  });

  it("should render action form when passed in", () => {
    const { getByTestId } = render(
      <MockTheme>
        <AddRoutineAction action={{ actionTypeID: ROUTINE_TYPE.Email }} />
      </MockTheme>
    );

    const emailBody = getByTestId("emailBody");
    expect(emailBody).not.toBeNull();
  });

  it("should render inventory action when launchdarkly is true", () => {
    mockFlags({ routinesInventoryArchive: true });
    const { getByTestId } = render(
      <MockTheme>
        <AddRoutineAction action={{ actionTypeID: ROUTINE_TYPE.Inventory }} />
      </MockTheme>
    );

    const ticketsExpiration = getByTestId("TicketsOlderThanDays");
    expect(ticketsExpiration).not.toBeNull();
  });

  it("should not render inventory action when launchdarkly is false", () => {
    mockFlags({ routinesInventoryArchive: false });
    const { queryByTestId } = render(
      <MockTheme>
        <AddRoutineAction action={{ actionTypeID: ROUTINE_TYPE.Inventory }} />
      </MockTheme>
    );

    const ticketsExpiration = queryByTestId("TicketsOlderThanDays");
    expect(ticketsExpiration).toBeNull();
  });

  it("should render text action form when passed in", () => {
    const { getByTestId } = render(
      <MockTheme>
        <AddRoutineAction action={{ actionTypeID: ROUTINE_TYPE.SMS }} />
      </MockTheme>
    );

    const mobileInput = getByTestId("phone-number");
    expect(mobileInput).not.toBeNull();
  });

  it("should render reversing lane action form when passed in", () => {
    useFeatureFlag.mockReturnValue(true);
    const { getByTestId } = render(
      <MockTheme>
        <AddRoutineAction action={{ actionTypeID: ROUTINE_TYPE.ReverseLane }} />
      </MockTheme>
    );

    const laneSelect = getByTestId("laneSelect");
    expect(laneSelect).not.toBeNull();
  });

  it("should not render text action list item when passed in non-text system event", () => {
    const wrapper = render(
      <MockTheme>
        <AddRoutineAction
          routine={{ systemEventId: SYSTEM_EVENTS.DOOR_CLOSED }}
        />
      </MockTheme>
    );
    expect(wrapper.queryByText("Text")).not.toBeInTheDocument();
  });

  it("should render text action list item when passed in text related system event", () => {
    const wrapper = render(
      <MockTheme>
        <AddRoutineAction
          routine={{ systemEventId: TEXT_ACTION_EVENTS.CALL_CENTER_CANCELLED }}
        />
      </MockTheme>
    );
    expect(wrapper.queryByText("Text")).toBeInTheDocument();
  });

  it("should not render occupancy action list item when passed in routine already has occupancy action", () => {
    const wrapper = render(
      <MockTheme>
        <AddRoutineAction
          routine={{ actions: [{ actionTypeID: 4, order: 1 }] }}
        />
      </MockTheme>
    );
    expect(wrapper.queryByText("Occupancy")).not.toBeInTheDocument();
    expect(wrapper.queryByText("Save Occupancy Count Snapshot")).not.toBeInTheDocument();
  });

  it("should not render non-reset action list item when passed in routine already has non-reset action", () => {
    const wrapper = render(
      <MockTheme>
        <AddRoutineAction
          routine={{ actions: [{ actionTypeID: 5, order: 1 }] }}
        />
      </MockTheme>
    );
    expect(wrapper.queryByText("Non-Reset")).not.toBeInTheDocument();
  });
});
