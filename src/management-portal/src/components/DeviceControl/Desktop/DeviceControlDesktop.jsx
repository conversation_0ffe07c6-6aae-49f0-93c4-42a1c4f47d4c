import React, {useRef} from "react";
import {Grid} from "@material-ui/core";
import {DeviceControlSidePanel} from "./DeviceControlSidePanel";
import {DeviceControlWindowPane} from "./DeviceControlWindowPane";

import {useStyles} from "./DeviceControlDesktop.style";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import {useTheme} from "@material-ui/core/styles";

export const DeviceControlDesktop = () => {
    const classes = useStyles();
    const windowPaneRef = useRef(null);
    const theme = useTheme();
    const isSmall = useMediaQuery(theme.breakpoints.down(1300));

    return (
        <div className={classes.wrapper}>
            <Grid container className={classes.container} spacing={1}>
                {!isSmall && (
                    <Grid item className={classes.sidePanelContainer}>
                        <DeviceControlSidePanel/>
                    </Grid>
                )}
                <Grid item style={{minHeight: "100%"}} xs>
                    <DeviceControlWindowPane windowPaneRef={windowPaneRef}/>
                </Grid>
            </Grid>
        </div>
    );
};