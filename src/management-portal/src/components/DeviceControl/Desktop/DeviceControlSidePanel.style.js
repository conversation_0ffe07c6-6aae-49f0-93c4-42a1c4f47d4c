import {makeStyles} from "@material-ui/core/styles";

export const useStyles = makeStyles((theme) => ({
    paperContainer: {
        height: "100%",
        display: "flex",
        flexDirection: "column",
        marginTop: theme.spacing(2),
        position: "relative",
        boxSizing: "border-box",
        overflow: "hidden",
        overscrollBehavior: "none",
    },
    filterInputContainer: {
        margin: theme.spacing(1, 1, 1, 1),
        flexShrink: 0,
    },
    treeViewContainer: {
        flex: 1,
        overflow: "auto",
        padding: "0 0 0 .5em",
        margin: 0,
        width: "100%",
        boxSizing: "border-box",
        overscrollBehavior: "contain",
        '&::-webkit-scrollbar': {
            width: '0.5em',
            height: '0'
        },
        '&::-webkit-scrollbar-track': {
            boxShadow: 'inset 0 0 6px rgba(0,0,0,0.00)',
            webkitBoxShadow: 'inset 0 0 6px rgba(0,0,0,0.00)'
        },
        '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(0,0,0,.1)',
        }
    }
}));