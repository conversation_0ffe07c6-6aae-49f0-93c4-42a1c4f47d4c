// File: components/EntityCardContent.js
import React, {useEffect, useMemo, useRef} from "react";
import PropTypes from "prop-types";
import {CardActions, CardContent} from "@material-ui/core";
import clsx from "clsx";
import {useStyles} from "./EntityCardState.styles";
import * as c from "../../../../constants";

// Components
import EntityToiletSwirl from "../../../Entities/Modules/ToiletSwirl";
import OccupancyMiniPanel from "../../../Occupancy/MiniPanel";
import {useListening} from "../../../../hooks/useDeviceListener/useDeviceListenerContext";
import EntityIconBar from "../../../Entities/Modules/EntityIconBar";
import {
    fillEntityLevel as coreFillEntityLevel, selectEntityIds,
    setEntityProperty as coreSetEntityProperty
} from "../../../../state/slices/CoreEntity";
import {setEntityProperty} from "../../../../state/slices/entities";
import {shallowEqual, useDispatch, useSelector} from "react-redux";
import DeviceStateService from "../../../../services/DeviceStateService";
import apiClient from "../../../../auth/apiClient";
import useCurrentUser from "../../../../hooks/useCurrentUser";

export const EntityCardState = ({entity, isDisabledByFeature, getDisabledFeatureName, openDetail, listeningState}) => {
    const classes = useStyles();

    return (
        <>
            <CardContent className={clsx(["card-content"])} onClick={openDetail}>
                <EntityToiletSwirl
                    entityID={entity.entityID}
                    disabled={isDisabledByFeature()}
                    isBeingListened={listeningState}
                />
                {entity.typeID === c.ENTITY_TYPE.Facility && (
                    <OccupancyMiniPanel entityId={entity.entityID}/>
                )}
                {entity.typeID === c.ENTITY_TYPE.Device &&
                    (entity.laneClosed === true ||
                        entity.disabled === true ||
                        entity.reverseOffline === true) && (
                        <span className={clsx([classes.closedMsg, "closed"])}>
                        LANE CLOSED
                    </span>
                    )}
                {entity.typeID === c.ENTITY_TYPE.Device && entity.lotFull && (
                    <span className={clsx([classes.closedMsg, "full"])}>
                    LOT FULL
                </span>
                )}
            </CardContent>

            <CardActions disableSpacing>
                {entity.typeID === c.ENTITY_TYPE.Device && (
                    <EntityIconBar
                        entityID={entity.entityID}
                        disabledFeature={getDisabledFeatureName()}
                        isBeingListened={listeningState}
                    />
                )}
            </CardActions>
        </>

    );
};

EntityCardState.propTypes = {
    entity: PropTypes.object.isRequired,
    isDisabledByFeature: PropTypes.func.isRequired,
    openDetail: PropTypes.func.isRequired,
    getDisabledFeatureName: PropTypes.func.isRequired,
};