import React from "react";
import PropTypes from "prop-types";
import {CardActions} from "@material-ui/core";
import * as c from "../../../../constants";
import EntityIconBar from "../../../Entities/Modules/EntityIconBar";
import {shallowEqual, useSelector} from "react-redux";
import {selectEntityById} from "../../../../state/slices/CoreEntity";
import {selectOrgTreeEntityById} from "../../../../state/slices/OrgTree";
import {useListening} from "../../../../hooks/useDeviceListener/useDeviceListenerContext";

export const EntityCardActions = ({entityID, getDisabledFeatureName}) => {
    const {isBeingListened} = useListening?.() || {isBeingListened: () => false};

    const entity = useSelector((state) => {
        let _entity = selectEntityById(state, entityID) ?? selectOrgTreeEntityById(state, entityID);
        return {
            entityID: _entity.entityid,
            typeID: _entity.typeid
        }
    }, shallowEqual);

    return (
        <CardActions disableSpacing>
            {entity.typeID === c.ENTITY_TYPE.Device && (
                <EntityIconBar
                    entityID={entity.entityID}
                    disabledFeature={getDisabledFeatureName()}
                    isBeingListened={isBeingListened(entity.entityID)}
                />
            )}
        </CardActions>
    );
};


EntityCardActions.propTypes = {
    entityID: PropTypes.object.isRequired,
    getDisabledFeatureName: PropTypes.func.isRequired,
};