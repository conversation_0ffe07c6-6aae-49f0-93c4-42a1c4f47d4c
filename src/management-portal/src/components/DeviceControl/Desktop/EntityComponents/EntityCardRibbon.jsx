import React from "react";
import PropTypes from "prop-types";
import {Card} from "@material-ui/core";
import {RibbonContainer, RightCornerLargeRibbon} from "react-ribbons";
import classNames from "classnames";
import {useStyles} from "./EntityCardRibbon.styles";
import EntityStatusBar from "../../../Entities/Modules/EntityStatusBar";
import {useListening} from "../../../../hooks/useDeviceListener/useDeviceListenerContext";

export const EntityCardRibbon = ({entity, isDisabledByFeature, measureRef, children, listeningState = null}) => {
    const classes = useStyles();
    const { isBeingListened } = useListening();

    return (
        <RibbonContainer className={classes.ribbonator}>
            {entity.lotFull && (
                <RightCornerLargeRibbon
                    backgroundColor="#ffcc00"
                    color="brown"
                    fontFamily="Arial"
                >
                    LOT FULL
                </RightCornerLargeRibbon>
            )}
            {(entity.laneClosed === true || entity.reverseOffline === true) && (
                <RightCornerLargeRibbon
                    backgroundColor="rgba(0,0,0,0.74)"
                    color="#fafafa"
                    fontFamily="Arial"
                >
                    CLOSED
                </RightCornerLargeRibbon>
            )}
            <Card
                variant="elevation"
                className={classNames(
                    classes.card,
                    entity.disabled === true ||
                    isDisabledByFeature() ||
                    entity.reverseOffline === true
                        ? "disabled"
                        : "",
                    entity.laneClosed === true || entity.reverseOffline === true
                        ? "closed"
                        : ""
                )}
                ref={measureRef}
                data-name={entity.name}
                data-id={entity.entityID}
                data-testid={entity.entityID}
            >
                <EntityStatusBar
                    entityID={entity.entityID}
                    disabled={isDisabledByFeature()}
                    isBeingListened={listeningState ?? isBeingListened(entity.entityID)}
                />
                {children}
            </Card>
        </RibbonContainer>
    );
};

EntityCardRibbon.propTypes = {
    entity: PropTypes.object.isRequired,
    isDisabledByFeature: PropTypes.func.isRequired,
    measureRef: PropTypes.func.isRequired,
    children: PropTypes.node
};