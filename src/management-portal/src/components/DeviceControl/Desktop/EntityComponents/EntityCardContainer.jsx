import React, {useEffect, useRef} from "react";
import PropTypes from "prop-types";
import Measure from "react-measure";
import {useEntityCardLogic} from "../../../../hooks/useEntityCardLogic";
import {EntityCardRibbon} from "./EntityCardRibbon";
import {EntityCardState} from "./EntityCardState";
import {useEntitySelectedContext} from "../../Hooks";
import {useListening} from "../../../../hooks/useDeviceListener/useDeviceListenerContext";
import {shallowEqual, useDispatch, useSelector} from "react-redux";
import {
    fillEntityLevel as coreFillEntityLevel,
    selectEntityIds,
    setEntityProperty as coreSetEntityProperty
} from "../../../../state/slices/CoreEntity";
import DeviceStateService from "../../../../services/DeviceStateService";
import apiClient from "../../../../auth/apiClient";
import useCurrentUser from "../../../../hooks/useCurrentUser";

export const EntityCardContainer = ({entityID}) => {
    const {
        entity,
        isDisabledByFeature,
        getDisabledFeatureName,
    } = useEntityCardLogic(entityID);
    const {handleEntityDetailSelection} = useEntitySelectedContext();
    const {isBeingListened, listeningEntities} = useListening?.() || {isBeingListened: () => false};
    const entityIds = useSelector((state) => selectEntityIds(state) , shallowEqual);
    const deviceStateService = new DeviceStateService(apiClient);
    const currentUser = useCurrentUser();
    const userID = useRef();
    const dispatch = useDispatch();

    const currentUserRef = useRef();
    currentUserRef.current = currentUser;
    userID.current = currentUser.UserID;
    const [listeningState, setListeningState] = React.useState(false);

    useEffect(() => {
        if(isBeingListened(entity.entityID) && listeningEntities && !entityIds.includes(entity.entityID)){
            Promise.resolve(dispatch(
                coreFillEntityLevel({
                    entityID: entity.entityID,
                    userID: userID.current,
                    updateContext: true,
                    isAdminPass: true
                }))).then(async () => {
                try {
                    // After entity level is filled, call deviceStateService.retrieveState asynchronously
                    const stateResult = await deviceStateService.retrieveState(entity.entityID);

                    if(stateResult.data.Devices[0] && stateResult.data.Devices[0].Connected) {
                        dispatch(
                            coreSetEntityProperty({
                                entityId: entity.entityID,
                                property: "state",
                                value: stateResult.data.Devices[0]?.Dsd ?? null,
                            })
                        );
                    }
                    setListeningState(true);
                } catch (error) {
                    console.error(`Error retrieving state for device ${entity.entityID}:`, error);
                }

            });
            return;
        }

        setListeningState(isBeingListened(entity.entityID));
    }, []);

    const handleClick = () => {
        handleEntityDetailSelection(entityID, true)
    };

    return (
        <Measure
            client
            key="measure"
        >
            {({measureRef}) => (
                <>
                    <EntityCardRibbon
                        entity={entity}
                        isDisabledByFeature={isDisabledByFeature}
                        measureRef={measureRef}
                        listeningState={listeningState}
                    >
                        <EntityCardState
                            entity={entity}
                            isDisabledByFeature={isDisabledByFeature}
                            getDisabledFeatureName={getDisabledFeatureName}
                            openDetail={handleClick}
                            listeningState={listeningState}
                        />
                    </EntityCardRibbon>
                </>
            )}
        </Measure>
    );
};

EntityCardContainer.defaultProps = {
    entityID: undefined
};

EntityCardContainer.propTypes = {
    entityID: PropTypes.string
};