import React from "react";
import PropTypes from "prop-types";
import {Button, Typography} from "@material-ui/core";
import clsx from "clsx";
import * as c from "../../../../constants";
import EntityExpandedView from "../../../EntityCard/Expanded/index";
import EntityProvisionButton from "../../../Entities/Modules/EntityProvisionButton";
import EntityDeleteButton from "../../../Entities/Modules/EntityDeleteButton";
import DeviceActionMenu from "../../../EntityCard/DeviceActionMenu";
import useHasPermissions from "../../../../hooks/useHasPermissions";
import {useStyles} from "./EntityExpandedSection.styles";
import {useListening} from "../../../../hooks/useDeviceListener/useDeviceListenerContext";
import {useHistory} from "react-router-dom";
import {shallowEqual, useSelector} from "react-redux";
import _ from "lodash";
import {useEntitySelectedContext} from "../../Hooks";

export const EntityExpandedSection = ({entityID}) => {
    const classes = useStyles();
    const {hasPermissions} = useHasPermissions();
    const {isBeingListened} = useListening?.() || {isBeingListened: () => false};
    const history = useHistory(); // this will be removed
    const contextID = useSelector(state => state.coreEntities.ContextID, shallowEqual);
    const entity = useSelector(state => {
        const entity = state.orgTree.entities[entityID];
        if (!entity) {
            return null;
        }
        const parentEntityTypeID = state.orgTree.entities[entity?.parententityid]?.typeid;
        const arrTree = Object.values(state.orgTree.entities);
        const hasChildren = arrTree.some((e) => e.parententityid === entity?.entityid);
        return {
            parentEntityID: entity?.parententityid,
            entityID: entity?.entityid,
            name: entity?.name,
            typeID: entity?.typeid,
            deviceMode:
                _.find(entity?.settings ?? [], {name: "devicemode"})?.value ?? null,
            reverseOffline: entity?.state?.ReversingLane?.ReverseOffline,
            hasChildren: hasChildren ?? false,
            parentEntityTypeID: parentEntityTypeID
        };
    }, shallowEqual);

    const {handleEntityDetailSelection, onSelectedEntity} = useEntitySelectedContext();

    const onClickCard = async () => {
        if (entity.typeID === c.ENTITY_TYPE.Device) {
            await onSelectedEntity({entityid: entity.parentEntityID});
            handleEntityDetailSelection(entity.parentEntityID, false);
            return
        }
        if (entity.typeID === c.ENTITY_TYPE.Tenant) {
            await onSelectedEntity({entityid: contextID});
            handleEntityDetailSelection(contextID, false);
            return
        }
        if(!entity.hasChildren && entity.parentEntityTypeID !== c.ENTITY_TYPE.Tenant) {
            await onSelectedEntity({entityid: entity.parentEntityID});
            handleEntityDetailSelection(entity.parentEntityID, false);
            return
        }
        if(!entity.hasChildren) {
            await onSelectedEntity({entityid: contextID});
            handleEntityDetailSelection(contextID, false);
            return
        }
        handleEntityDetailSelection(entity.entityID, false);
    }

    const provisionDevicePermission = hasPermissions(
        ["overview.devices.provisiondevice"],
        false,
        null
    );
    const onDelete = () => {
        history.go(0);
    }

    return (
        <div className={classes.entityExpandedContainer}>
            <div className={clsx(["entity-expanded-content",classes.entityExpandedContent])}>
                <EntityExpandedView entityId={entityID}/>
            </div>
            <div className={classes.entityExpandedActions}>
                {entity.typeID === c.ENTITY_TYPE.Device &&
                    provisionDevicePermission && (
                        <EntityProvisionButton
                            entityID={entityID}
                            className={clsx(["btn-provision"])}
                            data-name={entity.name}
                            isBeingListened={isBeingListened(entityID)}
                        />
                    )}
                <EntityDeleteButton
                    className={clsx(["btn-delete-entity", classes.deleteButton])}
                    data-name={entity.name}
                    entityID={entity.entityID}
                    variant="outlined"
                    size="small"
                    color="secondary"
                    onSuccess={onDelete}
                    isBeingListened={isBeingListened(entity.entityID)}
                />
                <div style={{flex: "1 0 0"}}/>
                {entity.typeID === c.ENTITY_TYPE.Device && (
                    <>
                        <DeviceActionMenu
                            entityID={entity.entityID}
                            entityName={entity.name}
                            deviceMode={entity.deviceMode}
                        />
                        <Typography>|</Typography>
                    </>
                )}

                <Button
                    onClick={onClickCard}
                    color="primary"
                    data-name={entity.name}
                    className="btn-close-detail"
                >
                    Close
                </Button>
            </div>
        </div>
    );
};


EntityExpandedSection.propTypes = {
    entityID: PropTypes.string.isRequired
};