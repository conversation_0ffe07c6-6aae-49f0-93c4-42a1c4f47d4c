import {makeStyles} from "@material-ui/core/styles";

export const useStyles = makeStyles((theme) => ({
    //color,position of lot-full, lane closed on card
    closedMsg: {
        color: theme.palette.amano.closed.main,
        position: "absolute",
        bottom: 32,
        right: 6,
        fontWeight: "bold",
        '&.full': {
            bottom: 48,
            color: theme.palette.warning.dark,
        }
    },
    entityExpandedContainer: {
        position: "relative",
        display: "flex",
        flexDirection: "column",
        height: "100%",
    },
    entityExpandedContent: {
        padding: theme.spacing(1)
    },
    deleteButton: {
        marginLeft: theme.spacing(1),
    },
    entityExpandedActions: {
        position: "sticky",
        bottom: 0,
        backgroundColor: theme.palette.background.paper,
        padding: theme.spacing(1),
        zIndex: 1,
        borderTop: `1px solid ${theme.palette.divider}`,
        display: "flex",
        alignItems: "center",
        marginTop: "auto", // Push to bottom when using flexbox
    },
}));