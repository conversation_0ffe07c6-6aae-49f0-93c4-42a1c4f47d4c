import {makeStyles} from "@material-ui/core/styles";

export const useStyles = makeStyles((theme) => ({
    wrapper: {
        position: "relative",
        width: "100%",
        height: "calc(100vh - 115px)",
        overflow: "hidden",
        overscrollBehavior: "none",
    },
    container: {
        height: "100%",
        display: "flex",
        flexWrap: "nowrap",
        overflow: "hidden",
        boxSizing: "border-box",
        padding: "1em",
        overscrollBehavior: "none",
    },
    sidePanelContainer: {
        height: "100%",
        width: "24em",
        flexShrink: 0,
        flexGrow: 0,
        padding: "1em",
        boxSizing: "border-box",
        scrollbarGutter: "stable",
        overscrollBehavior: "none",
        position: "relative",
        touchAction: "pan-y",
    },
}));