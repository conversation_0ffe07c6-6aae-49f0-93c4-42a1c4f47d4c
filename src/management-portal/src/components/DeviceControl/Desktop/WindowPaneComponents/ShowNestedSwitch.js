import React from "react";
import FormGroup from '@material-ui/core/FormGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Switch from '@material-ui/core/Switch';


export const ShowNestedSwitch = (props) => {
    return (
        <FormGroup row>
            <FormControlLabel
                control={
                    <Switch
                        checked={props.showNested}
                        onChange={props.handleChange}
                        name="checkedB"
                        color="primary"
                        disabled={props.disabled}
                    />
                }
                label="Show Nested"
                labelPlacement="start"
                style={{
                    marginRight: "-1px"
                }}
            />
        </FormGroup>
    );
};