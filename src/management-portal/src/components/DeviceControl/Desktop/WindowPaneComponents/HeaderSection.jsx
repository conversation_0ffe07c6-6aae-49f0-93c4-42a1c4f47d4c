import React from "react";
import {Box, Grid} from "@material-ui/core";
import {BreadCrumbTitle, ShowNestedSwitch} from "../WindowPaneComponents";
import {useStyles} from "./HeaderSection.styles";
import {useEntitySelectedContext} from "../../Hooks";
import Container from "@material-ui/core/Container";
import {AddEntityIcon, EntityDetailsIcon, FilterInput} from "../../Shared";
import EntityIconBar from "../../../Entities/Modules/EntityIconBar";
import {useListening} from "../../../../hooks/useDeviceListener/useDeviceListenerContext";
import {shallowEqual, useSelector} from "react-redux";
import {selectEntityForOrgTree} from "../../../../state/slices/OrgTree";
import {useEntityCardLogic} from "../../../../hooks/useEntityCardLogic";
import * as c from "../../../../constants";
import EntityAvatar from "../../../Entities/Modules/EntityAvatar";

export const HeaderSection = ({
                                  entityID,
                                  onFilterChange,
                                  showNested,
                                  handleShowNestedToggle
                              }) => {
    const classes = useStyles();
    const {entityDetailsState} = useEntitySelectedContext();
    const {isBeingListened} = useListening?.() || {isBeingListened: () => false};
    const entityType = useSelector((state) => selectEntityForOrgTree(entityID)(state)?.typeID, shallowEqual);
    const {
        getDisabledFeatureName,
        isDisabledByFeature
    } = useEntityCardLogic(entityID);

    const isOpenAndDevice =  entityDetailsState.isOpen && entityType === c.ENTITY_TYPE.Device;
    return (
        <Container className={classes.headerContainer} maxWidth={false}>
            <Container xs={isOpenAndDevice ? 12 : 7} className={classes.headerTitleContainer} disableGutters>
                <BreadCrumbTitle entityId={entityID}/>
            </Container>
            <Grid container xs={12} className={classes.headerUtilityContainer} wrap="wrap">
                {!entityDetailsState.isOpen &&
                    <>
                        <Box className={classes.utilityItemActionBtn}>
                            <AddEntityIcon entityId={entityID}/>
                            <EntityDetailsIcon entityId={entityID}/>
                        </Box>
                        <Box className={classes.utilityItemFilterBtn}>
                            <FilterInput onChange={onFilterChange} canExpand={false}/>
                        </Box>
                        <Box className={classes.utilityItemNestedBtn}>
                            <ShowNestedSwitch
                                handleChange={handleShowNestedToggle}
                                showNested={showNested}
                                disabled={entityType === c.ENTITY_TYPE.Area || entityType === c.ENTITY_TYPE.Tenant}
                            />
                        </Box>
                    </>
                }
                {isOpenAndDevice &&
                    <Box className={classes.headerUtilityContainer}>
                        <EntityIconBar
                            entityID={entityID}
                            disabledFeature={getDisabledFeatureName()}
                            isBeingListened={isBeingListened(entityID)}
                        />
                        <EntityAvatar
                            entityID={entityID}
                            iconSize="1x"
                            disabled={isDisabledByFeature()}
                            isBeingListened={isBeingListened(entityID)}
                        />
                    </Box>
                }
                {entityDetailsState.isOpen && entityType !== c.ENTITY_TYPE.Device &&
                    <Box className={classes.headerUtilityContainer}>
                        <AddEntityIcon entityId={entityID}/>
                    </Box>
                }
            </Grid>
        </Container>
    )
}