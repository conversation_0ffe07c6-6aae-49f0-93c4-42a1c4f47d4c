import {makeStyles} from "@material-ui/core/styles";

export const useStyles = makeStyles((theme) => ({
    headerContainer: {
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        padding: "8px 8px 8px 8px",
        [theme.breakpoints.down("md")]: {
        },
        flexWrap: "wrap",
        position: "sticky",
        top: 0,
        zIndex: 1,
        backgroundColor: theme.palette.background.paper,
    },
    headerTitleContainer: {
        padding: theme.spacing(1),
        margin: "0",
        [theme.breakpoints.down("md")]: {
            maxWidth: "100%"
        }
    },
    headerUtilityContainer: {
        display: "flex",
        justifyContent: "end",
        alignItems: "center",
        flexWrap: "wrap",
        gap: "10px"
    },
    utilityItemActionBtn: {
        display: 'flex',
        width: '16em',
        justifyContent: "space-evenly",
    },
    utilityItemFilterBtn: {
        flexGrow: 2
    },
    utilityItemNestedBtn: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        maxWidth:"fit-content"
    }
}));