import React, {useState} from "react";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>con<PERSON><PERSON><PERSON>, <PERSON>, Typography} from "@material-ui/core";
import {shallowEqual, useSelector} from "react-redux";
import * as c from "../../../../constants";
import {useStyles} from "./BreadCrumbTitle.style";
import {useEntitySelectedContext} from "../../Hooks";
import EditIcon from '@material-ui/icons/Edit';
import useHasPermissions from "../../../../hooks/useHasPermissions";
import {EntityDrawerEditor} from "../EntityComponents";

// Honestly just a preference to capitalize in the title
export function capitalizeFirstLetter(string) {
    if (!string) return string;
    return string.charAt(0).toUpperCase() + string.slice(1);
}

export const BreadCrumbTitle = ({entityId}) => {
    const classes = useStyles();
    const {
        onSelectedEntity,
        entityDetailsState,
        handleEntityDetailSelection
    } = useEntitySelectedContext();
    const {hasPermissions} = useHasPermissions();
    const isAdmin = useSelector((state) => state.user.isAdmin, shallowEqual);
    const entityType = useSelector((state) => state.orgTree.entities[entityId]?.typeid, shallowEqual);
    const [showEditDrawer, setShowEditDrawer] = useState(false);

    const EditDeviceDemographicsPermission = hasPermissions(
        ["overview.devices.editdemographics"],
        false,
        entityId
    );
    const EditFacilityDemographicsPermission = hasPermissions(
        ["overview.facilities.editdemographics"],
        false,
        entityId
    );
    const EditValetAreaDemographicsPermission = hasPermissions(
        ["overview.valetarea.editdemographics"],
        false,
        entityId
    );
    const addAreaPermission = hasPermissions(
        ["overview.area.addarea"],
        false,
        entityId
    );

    const entityDetails = useSelector((state) => {
        const foundEntity = state.orgTree.entities[entityId];
        if (!foundEntity) return {
            name: null,
            entityId: entityId,
            parentsName: null,
            parentEntityId: null
        };
        const parentEntity = state.orgTree.entities[foundEntity.parententityid];

        return {
            name: capitalizeFirstLetter(foundEntity.name),
            entityid: entityId,
            parentsName: capitalizeFirstLetter(parentEntity?.name),
            parententityid: foundEntity.parententityid,
            parentType: parentEntity?.typeid,
            typeid: foundEntity.typeid
        }
    }, shallowEqual);

    const onParentClick = (event) => {
        event.preventDefault();
        event.stopPropagation();

        if (entityDetails?.parentType === c.ENTITY_TYPE.Facility ||
            entityDetails?.parentType === c.ENTITY_TYPE.Area ||
            entityDetails?.parentType === c.ENTITY_TYPE.ValetArea ||
            entityDetails?.parentType === c.ENTITY_TYPE.FacilityGroup
        ) {
            onSelectedEntity({
                entityid: entityDetails.parententityid, // the parent is the entityId we want to populate
            });
            handleEntityDetailSelection(entityDetails.parententityid, false);
        }
    }

    const hasEditPermission = () => {
        switch (entityType) {
            case c.ENTITY_TYPE.Device:
                return EditDeviceDemographicsPermission;
            case c.ENTITY_TYPE.Facility:
                return EditFacilityDemographicsPermission;
            case c.ENTITY_TYPE.ValetArea:
                return EditValetAreaDemographicsPermission;
            case c.ENTITY_TYPE.FacilityGroup:
                return isAdmin;
            case c.ENTITY_TYPE.Area:
                return addAreaPermission;
            default:
                return false;
        }
    };
    const toggleShowEditor = () => {
        setShowEditDrawer(!showEditDrawer);
    };

    return (
        <div style={{display: "flex", alignItems: "center"}}>
            <Breadcrumbs aria-label="breadcrumb" className={classes.breadCrumbs}>
                {entityDetails?.parentType !== c.ENTITY_TYPE.Tenant && entityDetails?.parentsName &&
                    <Link href="/" onClick={onParentClick}>
                        <Typography variant={"h5"} color="primary" className={classes.entityTypography}>
                            {entityDetails.parentsName}
                        </Typography>
                    </Link>
                }
                <Typography
                    variant={"h5"}
                    color="primary"
                    className={classes.entityTypography}>{entityDetails.name}</Typography>
            </Breadcrumbs>
            {(entityDetailsState.isOpen && hasEditPermission()) &&
                <IconButton
                    color="action"
                    component="div"
                    onClick={toggleShowEditor}
                >
                    <EditIcon color={"primary"}/>
                </IconButton>
            }
            <EntityDrawerEditor
                entityID={entityId}
                onClose={toggleShowEditor}
                showEditDrawer={showEditDrawer}
            />
        </div>
    );
};