import React, {useEffect, useRef} from "react";
import {Box, CircularProgress, Divider, Grid, Paper, Typography} from "@material-ui/core";
import {useStyles} from "./DeviceControlWindowPane.style";
import {useEntityFiltering, useEntitySelectedContext} from "../Hooks";
import * as c from "../../../constants";
import {HeaderSection, SubSectionTitle} from "./WindowPaneComponents";
import {shallowEqual, useSelector} from "react-redux";
import {selectEntityForOrgTree} from "../../../state/slices/OrgTree";
import {EntityCardContainer, EntityExpandedSection} from "./EntityComponents";
import clsx from 'clsx';
import {useFeatureFlag} from "../../../hooks/useFeatureFlags";

export const DeviceControlWindowPane = ({windowPaneRef}) => {
    const classes = useStyles();
    const containerRef = windowPaneRef;

    const {associatedEntities, entitySelected, isLoading, entityDetailsState} = useEntitySelectedContext();

    const entity = useSelector((state) => selectEntityForOrgTree(entitySelected)(state), shallowEqual);
    const contextID = useSelector(state => state.coreEntities.ContextID, shallowEqual);
    const contractNestFeature = useFeatureFlag("Contract Nest");

    const [showNested, setShowNested] = React.useState(true);
    const [filterText, setFilterText] = React.useState('');
    const contentRef = useRef(null);

    const filteredAssociatedEntities = useEntityFiltering(
        associatedEntities,
        filterText,
        showNested,
        entity
    );

    // Add content-ready class when data is loaded
    useEffect(() => {
        if (!isLoading && contentRef.current) {
            contentRef.current.classList.add(classes.contentReady);
        }
    }, [isLoading, classes.contentReady]);

    // Reset animation when data changes
    useEffect(() => {
        if (contentRef.current) {
            contentRef.current.classList.remove(classes.contentReady);
            // Force browser to recognize the change before re-adding
            void contentRef.current.offsetWidth;

            if (!isLoading) {
                contentRef.current.classList.add(classes.contentReady);
            }
        }
    }, [entitySelected, filterText, isLoading, classes.contentReady, entityDetailsState.isOpen]);

    const handleShowNestedToggle = (event) => {
        setShowNested(event.target.checked);
    };

    const handleFilterChange = (value) => {
        setFilterText(value);
    };

    const sortedResult = {};

    const entityMap = Object.entries(filteredAssociatedEntities).reduce((acc, [key, value]) => {
        acc[value.entityID] = {key, value};
        return acc;
    }, {});

    // Sorting entries so parents and children are together
    const sortedEntries = Object.entries(filteredAssociatedEntities).sort(([keyA, a], [keyB, b]) => {
        if (entityMap[a.parentID]) {
            if (b.entityID === a.parentID) return 1;
            return entityMap[a.parentID].key.localeCompare(keyB);
        }
        if (b.parentID === a.entityID) return -1;

        const parentA = a.parentID || '';
        const parentB = b.parentID || '';
        return parentA.localeCompare(parentB);
    });

    sortedEntries.forEach(([key, value]) => {
        sortedResult[key] = value;
    });

    if (isLoading) {
        return (
            <Paper elevation={3} className={classes.paperContainer} ref={containerRef}>
                <Box
                    height="75vh"
                    display="flex"
                    flexDirection="column"
                    alignItems="center"
                    justifyContent="center"
                >
                    <CircularProgress size="3rem"/>
                    <Typography variant="h5">Preparing</Typography>
                </Box>
            </Paper>
        );
    }

    return (
        <Paper elevation={3} className={classes.paperContainer} ref={containerRef}>
            <HeaderSection
                entityID={entityDetailsState.entityID !== "" ? entityDetailsState.entityID : entitySelected}
                onFilterChange={handleFilterChange}
                showNested={showNested}
                handleShowNestedToggle={handleShowNestedToggle}
            />
            <Divider variant="middle"/>
            {!isLoading && (
                <Grid
                    spacing={2}
                    className={clsx(
                        entityDetailsState.isOpen ? classes.detailsContainer : classes.gridContainer
                    )}
                    ref={contentRef}
                >
                    {filteredAssociatedEntities && !entityDetailsState.isOpen && (
                        Object.entries(sortedResult).map(([title, entities], sectionIndex) => {
                            const needsSubTitle =
                                !(entity?.name === title || entity?.entityid === title) &&
                                entities.children.filter((entity) => entity?.typeid === c.ENTITY_TYPE.Device).length > 0;

                            if(entities.typeID === c.ENTITY_TYPE.Area && !contractNestFeature && entities.parentID === contextID) return null;
                            return (
                                <React.Fragment key={`section-${sectionIndex}`}>
                                    {needsSubTitle && (
                                        <Grid item xs={12}>
                                            <SubSectionTitle title={title} entityID={entities.entityID}/>
                                        </Grid>
                                    )}

                                    {entities.children
                                        .filter(
                                            (entity) =>
                                                entity.typeid === c.ENTITY_TYPE.Device ||
                                                c.ENTITY_TYPE[entity?.entitytype] === c.ENTITY_TYPE.Device
                                        )
                                        .map((entity, entityIndex) => (
                                            <div key={`entity-${sectionIndex}-${entityIndex}`}>
                                                <Grid
                                                    item
                                                    className={clsx(
                                                        classes.cardContainer,
                                                        classes.entityCard
                                                    )}
                                                    data-testid="entity-card"
                                                >
                                                    <EntityCardContainer entityID={entity.entityid}/>
                                                </Grid>
                                            </div>
                                        ))}
                                </React.Fragment>
                            );
                        })
                    )}
                </Grid>
            )}
            {entityDetailsState.isOpen && <EntityExpandedSection entityID={entityDetailsState.entityID}/>}
        </Paper>
    );
};