import {makeStyles} from "@material-ui/core/styles";

export const useStyles = makeStyles((theme) => ({
    paperContainer: {
        height: "100%",
        marginTop: theme.spacing(2),
        position: "relative",
        boxSizing: "border-box",
        overflow: "scroll",
        display: "flex",
        flexDirection: "column",

        // Custom scrollbar styling
        '&::-webkit-scrollbar': {
            width: '0.5em',
            height: '0'
        },
        '&::-webkit-scrollbar-track': {
            boxShadow: 'inset 0 0 6px rgba(0,0,0,0.00)',
            webkitBoxShadow: 'inset 0 0 6px rgba(0,0,0,0.00)'
        },
        '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(0,0,0,.1)',
        }
    },
    gridContainer: {
        flexWrap: "wrap",
        flexDirection: "row",
        padding: theme.spacing(1),
        display: "flex",
    },
    detailsContainer: {
        display: "flex",
        flexDirection: "column",
        flex: 1,  // Take up remaining space
        height: "100%", // Use full height
    },
    // Rest of your styles remain unchanged
    devicePaper: {
        minWidth: 300,
        padding: theme.spacing(2),
        textAlign: "center",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: 100, // You can adjust this height as needed
        transition: "transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out",
        "&:hover": {
            transform: "translateY(-5px)",
            boxShadow: theme.shadows[4],
        },
    },
    sectionTitle: {
        color: 'rgba(0, 0, 0, 0.38)'
    },
    headerContainer: {
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        padding: "8px 8px 8px 8px",
    },
    headerTitleContainer: {
        padding: theme.spacing(1),
    },
    cardContainer: {
        margin: theme.spacing(1)
    },
    headerUtilityContainer: {
        display: "flex",
        justifyContent: "end",
        alignItems: "center",
    },
    utilityItemActionBtn: {
        display: 'flex',
        width: '77px',
    },
    utilityItemFilterBtn: {
        padding: "0 15px 0 15px",
    },
    utilityItemNestedBtn: {
        padding: "0 8px 0 8px",
    },
    headerTitle: {
        color: theme.palette.primary.dark,
    },
    // Animation classes
    entityCard: {
        opacity: 0,
        transform: 'translateY(10px)',
        visibility: 'hidden',
    },
    contentReady: {
        '& $entityCard': {
            visibility: 'visible',
            animation: '$fadeInUp 0.4s ease-out forwards',
        },
        '& $entityCard:nth-child(1)': { animationDelay: '0ms' },
        '& $entityCard:nth-child(2)': { animationDelay: '30ms' },
        '& $entityCard:nth-child(3)': { animationDelay: '60ms' },
        '& $entityCard:nth-child(4)': { animationDelay: '90ms' },
        '& $entityCard:nth-child(5)': { animationDelay: '120ms' },
        '& $entityCard:nth-child(n+6)': { animationDelay: '150ms' },
    },
    '@keyframes fadeInUp': {
        '0%': {
            opacity: 0,
            transform: 'translateY(10px)',
        },
        '100%': {
            opacity: 1,
            transform: 'translateY(0)',
        },
    },
}));