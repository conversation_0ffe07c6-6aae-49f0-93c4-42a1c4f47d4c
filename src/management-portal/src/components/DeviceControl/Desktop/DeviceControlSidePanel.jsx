import React, {useEffect, useMemo, useState} from "react";
import {Divider, Paper} from "@material-ui/core";
import {useStyles} from "./DeviceControlSidePanel.style";
import {fuzzyMatch, useEntitiesWithSeverity, useEntitySelectedContext, useSidePanelExpansion} from "../Hooks";
import TreeView from "@material-ui/lab/TreeView";
import {StyledTreeItem} from "./StyledTreeItem";
import {shallowEqual, useSelector} from "react-redux";
import {selectOrgTreeData} from "../../../state/slices/OrgTree";
import {MinusSquare, PlusSquare} from "./Icons";
import PropTypes from "prop-types";
import {FilterInput} from "../Shared";
import Container from "@material-ui/core/Container";
import _ from 'lodash';
import EntityService from "../../../services/EntityService";
import apiClient from "../../../auth/apiClient";
import {useFeatureFlag} from "../../../hooks/useFeatureFlags";
import * as c from "../../../constants";


const entityService = new EntityService(apiClient);

export const DeviceControlSidePanel = () => {
    const classes = useStyles();
    const {entitySelected, isLoading} = useEntitySelectedContext();
    const contractNestFeature = useFeatureFlag("Contract Nest");
    const [facilitiesByScope, setFacilitiesByScope] = useState([]);
    const contextID = useSelector(state => state.coreEntities.ContextID, shallowEqual);
    const treeData = useSelector(
        state => selectOrgTreeData(state, facilitiesByScope),
        _.isEqual
    );

    useEffect(() => {
        async function getFacilitiesByScope() {
            const result = await entityService.getPropertiesByScopes();

            const hasContextIdInTree = (node) => {
                if (node.id === contextID) return true;
                if (node.children) {
                    return node.children.some(child => hasContextIdInTree(child));
                }
                return false;
            };

            const topLevelIds = result.data
                .filter(item => !hasContextIdInTree(item))
                .map(item => item.id);

            topLevelIds.push(contextID);
            setFacilitiesByScope(topLevelIds);
        }

        getFacilitiesByScope();
    }, [contextID]);

    const [filterText, setFilterText] = useState('');

    const {entitiesWithSeverity} = useEntitiesWithSeverity();
    const {entitiesToExpand, defaultSelected} = useSidePanelExpansion();
    const handleFilterChange = (value) => {
        setFilterText(value);
    };

    const renderTree = nodes => {
        console.log("nodes", nodes);

        // if (nodes?.typeid === c.ENTITY_TYPE.Area && nodes.parententityid === contextID && !contractNestFeature) return null;
        return (
            <StyledTreeItem
                entity={nodes}
                key={nodes?.entityid}
                severityInfo={entitiesWithSeverity[nodes?.entityid]?.SeverityInfo}
            >
                {Array.isArray(nodes?.children) ? nodes?.children.map((node) => {

                    return renderTree(node)
                }) : null}
            </StyledTreeItem>
        )
    };

    const doesNodeMatch = (node) => {
        if (!node || !filterText.trim()) return false;

        return (
            fuzzyMatch(node.name || '', filterText) ||
            fuzzyMatch(node.entityid || '', filterText) ||
            fuzzyMatch(node.details?.description || '', filterText)
        );
    };

    const filterTreeRecursive = (node) => {
        if (!node) return null;

        // Check if the current node matches
        const nodeMatches = doesNodeMatch(node);

        // Recursively filter children
        let filteredChildren = [];
        if (Array.isArray(node.children) && node.children.length > 0) {
            // Process all children recursively
            filteredChildren = node.children
                .map(child => filterTreeRecursive(child))
                .filter(Boolean); // Remove null results
        }

        // Keep this node if:
        // 1. There's no filter text (show everything)
        // 2. This node matches the filter
        // 3. Any of its children match the filter
        if (!filterText.trim() || nodeMatches || filteredChildren.length > 0) {
            return {
                ...node,
                children: filteredChildren
            };
        }

        return null;
    };

    const filteredTreeData = useMemo(() => {
        return filterTreeRecursive(treeData);
    }, [treeData, filterText]);

    console.log("treeData", filteredTreeData);


    return (
        <Paper elevation={3} className={classes.paperContainer}>
            <div className={classes.filterInputContainer}>
                <FilterInput onChange={handleFilterChange}/>
            </div>
            <Divider/>
            <Container className={classes.treeViewContainer}>
                {!isLoading && treeData && (
                    <TreeView
                        defaultExpanded={entitiesToExpand}
                        defaultSelected={defaultSelected}
                        selected={entitySelected}
                        defaultCollapseIcon={<MinusSquare/>}
                        defaultExpandIcon={<PlusSquare/>}
                    >
                        {renderTree(filteredTreeData)}
                    </TreeView>
                )}
            </Container>
        </Paper>
    );
}

DeviceControlSidePanel.propTypes = {
    onSelected: PropTypes.string
};
