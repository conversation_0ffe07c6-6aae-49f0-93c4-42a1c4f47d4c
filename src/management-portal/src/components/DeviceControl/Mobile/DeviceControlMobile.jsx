import React from "react";
import {useStyles} from "./DeviceControlMobile.styles";
import {FilterInput} from "../Shared";
import {useEntityFiltering, useEntitySelectedContext} from "../Hooks";
import {shallowEqual, useSelector} from "react-redux";
import {selectEntityForOrgTree} from "../../../state/slices/OrgTree";
import * as c from "../../../constants";
import {EntityDropDown} from "./EntityDropDown";

export const DeviceControlMobile = () => {
    const classes = useStyles();
    const [filterText, setFilterText] = React.useState('');
    const {associatedEntities, entitySelected} = useEntitySelectedContext();
    const entity = useSelector((state) => selectEntityForOrgTree(entitySelected)(state), shallowEqual);
    const filteredAssociatedEntities = useEntityFiltering(
        associatedEntities,
        filterText,
        true,
        entity
    )

    const handleFilterChange = (value) => {
        setFilterText(value);
    };


    return (
        <div className={classes.mobileContainer}>
            <div className={classes.filterInputContainer}>
                <FilterInput onChange={handleFilterChange}/>
            </div>
            <div className={classes.entityListContainer}>
                {filteredAssociatedEntities &&
                    Object.entries(filteredAssociatedEntities).map(([title, entities]) => {
                        const showEntityDropdown = (entities.children.filter((entity) => entity?.typeid === c.ENTITY_TYPE.Device).length > 0);
                        return (
                            <div key={title}>
                                {showEntityDropdown && (
                                    <EntityDropDown
                                        title={title}
                                        entities={entities.children}
                                        entityID={entities.entityID}
                                        hasChildren={false}
                                    />
                                )}
                            </div>
                        );
                    })
                }
            </div>
        </div>
    );
};