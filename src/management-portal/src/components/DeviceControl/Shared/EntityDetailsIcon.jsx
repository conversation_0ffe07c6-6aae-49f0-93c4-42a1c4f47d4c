import React from "react";
import {Button, IconButton} from "@material-ui/core";
import {useEntitySelectedContext} from "../Hooks";
import {useStyles} from "./Icon.styles";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import {useTheme} from "@material-ui/core/styles";
import SettingsIcon from "@material-ui/icons/Settings";

export const EntityDetailsIcon = ({entityId}) => {
    const {handleEntityDetailSelection} = useEntitySelectedContext();
    const classes = useStyles();
    const onIconClick = () => {
        handleEntityDetailSelection(entityId, true);
    };
    const theme = useTheme();

    const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

    if(isMobile)
        return (
            <>
                <IconButton
                    color="action"
                    aria-label="Open Details"
                    component="div"
                    onClick={onIconClick}
                    className={classes.root}
                >
                    <SettingsIcon fontSize={"inherit"}/>
                </IconButton>
            </>
        );

    return (
        <>
            <Button
                onClick={onIconClick}
                variant="outlined"
                size="small"
                color="primary"
                className={classes.root}
            >
                Details
            </Button>
        </>
    )
};