import {makeStyles} from "@material-ui/core/styles";

export const useStyles = makeStyles((theme) => ({
    root: {
        flexGrow: 1,
    },
    menuButton: {
        marginRight: theme.spacing(2),
    },
    title: {
        flexGrow: 1,
        display: 'none',
        [theme.breakpoints.up('sm')]: {
            display: 'block',
        },
    },
    searchContainer: {
        position: 'relative',
        width: '100%',
        [theme.breakpoints.down("sm")]: {
            margin: theme.spacing(2, 1, 1, 1),
            maxWidth: 'unset',
        }
    },
    searchIcon: {
        padding: theme.spacing(0, 2),
        height: '100%',
        position: 'absolute',
        pointerEvents: 'none',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    },
    inputRoot: {
        color: 'inherit',
        width: '100%',
    },
    inputInput: {
        padding: theme.spacing(1, 1, 1, 0),
        // vertical padding + font size from searchIcon
        width: '100%',
        paddingLeft: `calc(1em + ${theme.spacing(4)}px)`
    },
    inputInputExpand: {
        padding: theme.spacing(1, 1, 1, 0),
        // vertical padding + font size from searchIcon
        paddingLeft: `calc(1em + ${theme.spacing(4)}px)`,
        transition: theme.transitions.create('width', {
            duration: '0.3s', // Add duration to make transition more noticeable
            easing: theme.transitions.easing.easeInOut
        }),

        width: '25ch',
        [theme.breakpoints.up('sm')]: {
            '&:focus': {
                width: '33ch',
            },
        },
    },
}));