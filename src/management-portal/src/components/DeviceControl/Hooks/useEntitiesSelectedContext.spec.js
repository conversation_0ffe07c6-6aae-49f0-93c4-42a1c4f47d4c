import React from 'react';
import {act, renderHook} from "@testing-library/react-hooks";
import {Provider, useDispatch, useSelector} from 'react-redux';
import {EntitySelectedProvider, useEntitySelectedContext} from './useEntitiesSelectedContext';
import {fillEntityLevel, selectContextEntityFlat} from "../../../state/slices/CoreEntity";
import {configureStore} from '@reduxjs/toolkit';
import apiClient from "../../../auth/apiClient";
import {useEnqueueSnackbar} from "../../../hooks/useEnqueueSnackbar";

// Mock the necessary dependencies
jest.mock("react-redux", () => ({
    ...jest.requireActual("react-redux"),
    useSelector: jest.fn(),
    useDispatch: jest.fn()
}));

jest.mock("../../../state/slices/CoreEntity", () => ({
    fillEntityLevel: jest.fn(),
    fillAssociatedEntityLevel: jest.fn(),
    selectContextEntityFlat: jest.fn()
}));
jest.mock("../../../hooks/useEnqueueSnackbar");

jest.mock("../../../auth/apiClient", () => ({
    get: jest.fn()
}));

// Create default test data
const defaultTestData = {
    contextID: "context-123",
    contextEntities: {
        contextID: "context-123",
        children: [
            {entityid: "child-1", parententityid: "context-123", name: "Child 1"},
            {entityid: "child-2", parententityid: "context-123", name: "Child 2"}
        ]
    },
    orgEntities: {
        "context-123": {
            entityid: "context-123",
            name: "Context Entity",
            details: true
        },
        "child-1": {
            entityid: "child-1",
            parententityid: "context-123",
            name: "Child 1",
            details: true
        },
        "child-2": {
            entityid: "child-2",
            parententityid: "context-123",
            name: "Child 2",
            details: true
        },
        "entity-456": {
            entityid: "entity-456",
            parententityid: "context-123",
            name: "Entity 456",
            details: true,
            entityname: "Entity 456"
        }
    },
    apiResponse: {
        data: [
            {entityid: "child-1", parententityid: "context-123", name: "Child 1"},
            {entityid: "child-2", parententityid: "context-123", name: "Child 2"},
            {entityid: "context-123", name: "Context Entity"}
        ]
    },
    newEntityApiResponse: {
        data: [
            {entityid: "entity-456", parententityid: "new-context-123", name: "Entity 456"},
            {entityid: "child-of-456", parententityid: "new-context-123", name: "Child of new-context-123"},
            {entityid: "new-context-123", name: "Context Entity"}
        ]
    }
};

const defaultStoreState = {
    coreEntities: {
        ContextID: defaultTestData.contextID
    },
    orgTree: {
        entities: defaultTestData.orgEntities
    },
    user: {
        UserID: "user-123"
    }
};

const mockStore = configureStore({
    reducer: (state = defaultStoreState) => state
});

describe('useEntitySelectedContext', () => {
    const mockDispatch = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        useDispatch.mockReturnValue(mockDispatch);
        apiClient.get.mockResolvedValue(defaultTestData.apiResponse);
    });

    // Helper function to wait for all pending promises and state updates
    const flushPromises = () => new Promise(resolve => setTimeout(resolve, 0));

    it('initializes with context entities when available', async () => {

        const expectedAssociatedEntities = {
            'Context Entity': {
                entityID: 'context-123',
                children: [
                    {
                        details: true,
                        entityid: "child-1",
                        name: "Child 1",
                        parententityid: "context-123"
                    },
                    {
                        details: true,
                        entityid: "child-2",
                        name: "Child 2",
                        parententityid: "context-123"
                    },
                    {
                        details: true,
                        entityid: "entity-456",
                        name: "Entity 456",
                        parententityid: "context-123",
                        entityname: "Entity 456"
                    }
                ]
            }
        }
        // Configure the mock to return context entities
        useSelector.mockImplementation((selector) => {
            if (selector === selectContextEntityFlat) {
                return defaultTestData.contextEntities;
            }
            return selector(defaultStoreState);
        });

        // Create a wrapper with the provider
        const wrapper = ({children}) => (
            <Provider store={mockStore}>
                <EntitySelectedProvider>{children}</EntitySelectedProvider>
            </Provider>
        );

        // Use renderHook inside act to properly handle state updates
        let result;
        await act(async () => {
            const renderedHook = renderHook(() => useEntitySelectedContext(), {wrapper});
            result = renderedHook.result;
            await flushPromises();
        });

        // Assert on the result
        expect(result.current.entitySelected).toBe(defaultTestData.contextID);
        expect(result.current.associatedEntities).toEqual(expectedAssociatedEntities);
    });

    it('should fetch entities when context entities are not available', async () => {
        // Configure mock to return null for contextEntities
        useSelector.mockImplementation((selector) => {
            if (selector === selectContextEntityFlat) {
                return null; // No context entities
            }
            return selector({
                ...defaultStoreState,
                coreEntities: {
                    ...defaultStoreState.coreEntities,
                    entities: {
                        [defaultTestData.contextID]: {
                            entityid: defaultTestData.contextID,
                            parententityid: null,  // This makes it the root entity
                            name: "Context Entity"
                        }
                    }
                }
            });
        });

        const wrapper = ({children}) => (
            <Provider store={mockStore}>
                <EntitySelectedProvider>{children}</EntitySelectedProvider>
            </Provider>
        );

        // Render the hook with act
        await act(async () => {
            renderHook(() => useEntitySelectedContext(), {wrapper});
            await flushPromises();
        });

        // Verify API call
        expect(apiClient.get).toHaveBeenCalledWith(`entities/${defaultTestData.contextID}?limit=4`);
    });

    it('should not change selected entity if selecting the same entity', async () => {
        // Configure mocks
        useSelector.mockImplementation((selector) => {
            if (selector === selectContextEntityFlat) {
                return defaultTestData.contextEntities;
            }
            return selector(defaultStoreState);
        });

        const wrapper = ({children}) => (
            <Provider store={mockStore}>
                <EntitySelectedProvider>{children}</EntitySelectedProvider>
            </Provider>
        );

        let result;
        await act(async () => {
            const renderedHook = renderHook(() => useEntitySelectedContext(), {wrapper});
            result = renderedHook.result;
            await flushPromises();
        });

        // Clear previous API calls
        apiClient.get.mockClear();

        // Select the same entity
        await act(async () => {
            await result.current.onSelectedEntity({entityid: defaultTestData.contextID});
            await flushPromises();
        });

        // No API call should be made for the same entity
        expect(apiClient.get).not.toHaveBeenCalled();
    });


    it('should use data from store if entity details exist in OrgEntities', async () => {
        useSelector.mockImplementation((selector) => {
            if (selector === selectContextEntityFlat) {
                return defaultTestData.contextEntities;
            }
            return selector(defaultStoreState);
        });

        const wrapper = ({children}) => (
            <Provider store={mockStore}>
                <EntitySelectedProvider>{children}</EntitySelectedProvider>
            </Provider>
        );

        let result;
        await act(async () => {
            const renderedHook = renderHook(() => useEntitySelectedContext(), {wrapper});
            result = renderedHook.result;
            await flushPromises();
        });

        // Clear API call history
        apiClient.get.mockClear();

        // Select an entity that exists in the store
        await act(async () => {
            await result.current.onSelectedEntity({
                entityid: "entity-456",
                parententityid: defaultTestData.contextID
            });
            await flushPromises();
        });

        // Should not make an API call since the entity details exist in OrgEntities
        expect(apiClient.get).not.toHaveBeenCalled();

        // The entity should be selected
        expect(result.current.entitySelected).toBe("entity-456");
    });

    it('should fetch new entity data if not found in store', async () => {
        // Mock entity not found in store
        const newEntity = {
            entityid: "new-context-123",
            parententityid: defaultTestData.contextID
        };

        const modifiedOrgEntities = {
            ...defaultTestData.orgEntities,
            "new-context-123": {
                entityid: "new-context-123",
                parententityid: defaultTestData.contextID,
                // No details property means it doesn't exist fully in the store
            }
        };

        useSelector.mockImplementation((selector) => {
            if (selector === selectContextEntityFlat) {
                return defaultTestData.contextEntities;
            }
            if (typeof selector === 'function') {
                return selector({
                    ...defaultStoreState,
                    orgTree: {
                        entities: modifiedOrgEntities
                    }
                });
            }
            return undefined;
        });

        apiClient.get.mockResolvedValue(defaultTestData.newEntityApiResponse);

        const wrapper = ({children}) => (
            <Provider store={mockStore}>
                <EntitySelectedProvider>{children}</EntitySelectedProvider>
            </Provider>
        );

        let result;
        await act(async () => {
            const renderedHook = renderHook(() => useEntitySelectedContext(), {wrapper});
            result = renderedHook.result;
            await flushPromises();
        });

        // Clear API call history
        apiClient.get.mockClear();

        // Select a new entity not in the store
        await act(async () => {
            await result.current.onSelectedEntity(newEntity);
            await flushPromises();
        });

        // Should make an API call
        expect(apiClient.get).toHaveBeenCalledWith(`entities/${newEntity.entityid}?limit=4`);

        // Should dispatch to fill associated entity level
        expect(mockDispatch).toHaveBeenCalledWith(fillEntityLevel(expect.any(Object)));
    });


    it('should handle empty API response', async () => {
        // Mock empty API response
        apiClient.get.mockResolvedValue({data: []});
        useEnqueueSnackbar.mockReturnValue(jest.fn());
        useSelector.mockImplementation((selector) => {
            if (selector === selectContextEntityFlat) {
                return defaultTestData.contextEntities;
            }
            return selector(defaultStoreState);
        });

        const wrapper = ({children}) => (
            <Provider store={mockStore}>
                <EntitySelectedProvider>{children}</EntitySelectedProvider>
            </Provider>
        );

        let result;
        await act(async () => {
            const renderedHook = renderHook(() => useEntitySelectedContext(), {wrapper});
            result = renderedHook.result;
            await flushPromises();
        });

        // Initial selection should be the context ID
        expect(result.current.entitySelected).toBe(defaultTestData.contextID);

        // Select an entity that will return empty response
        await act(async () => {
            await result.current.onSelectedEntity({
                entityid: "empty-response-entity",
                parententityid: defaultTestData.contextID
            });
            await flushPromises();
        });

        // State should not change from the initial selection
        expect(result.current.entitySelected).toBe(defaultTestData.contextID);
    });
});