import React from "react";
import { useStyles } from "./styles";
import Switch from "@material-ui/core/Switch";
import { FormControlLabel } from "@material-ui/core";
import FormGroup from "@material-ui/core/FormGroup";
import Box from "@material-ui/core/Box";
import Typography from "@material-ui/core/Typography";
import IconButton from "@material-ui/core/IconButton";
import EditIcon from "@material-ui/icons/Edit";
import CheckIcon from "@material-ui/icons/Check";
import CloseIcon from "@material-ui/icons/Close";
import { TextField } from "formik-material-ui";
import Alert from "../Alert";
import useSettingsContext from "../../hooks/useSettingsContext";
import useHasPermissions from "../../hooks/useHasPermissions";
import { Formik, Form, setIn, Field } from "formik";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import { useTheme } from "@material-ui/core/styles";
import useCurrentFacility from "../../hooks/useCurrentFacility";

const GracePeriodForm = () => {
  const theme = useTheme();
  const { facilityID } = useCurrentFacility();
  const isLargeScreen = useMediaQuery(theme.breakpoints.up("sm"));
  const {
    getSettingByName,
    updateSetting,
    deleteSettingByName
  } = useSettingsContext();
  const classes = useStyles();
  const [gracePeriodState, setGracePeriodState] = React.useState(null);
  const [alert, setAlert] = React.useState(null);

  const { hasPermissions } = useHasPermissions();
  const settingsWrite = hasPermissions(["settings.write"]);

  const handleSubmit = async value => {
    await updateSetting(facilityID, {
      name: "GracePeriod",
      value: value.time.toString()
    })
      .then(result => {
        setGracePeriodState({
          editing: false,
          active: true,
          amount: result.data.settingValue,
          hasStoredPeriod: true
        });
        setAlert({
          open: true,
          message: `grace period set to ${result.data.settingValue} minutes`,
          type: "success"
        });
      })
      .catch(() => {
        setAlert({
          open: true,
          message: "we ran in to an issue trying to update the grace period",
          type: "error"
        });
      });
  };

  const handleToggle = () => {
    if (settingsWrite) {
      gracePeriodState ? disableGracePeriod() : activateGracePeriod();
    }
  };

  const activateGracePeriod = () => {
    setGracePeriodState({
      editing: true,
      amount: 5,
      active: true,
      hasStoredPeriod: false
    });
  };

  const validate = value => {
    let errors = {};
    const parsed = parseInt(value.time);
    if (isNaN(parsed) || value.time.toString().includes("e")) {
      errors = setIn(errors, "time", "must be a valid number");
    } else if (parsed < 0) {
      errors = setIn(errors, "time", "time cannot be negative");
    }
    return errors;
  };

  const disableGracePeriod = () => {
    if (!gracePeriodState.hasStoredPeriod) {
      setGracePeriodState(null);
      return;
    }
    deleteSettingByName(facilityID, "GracePeriod")
      .then(() => {
        setGracePeriodState(null);
        setAlert({
          open: true,
          message: "grace period disabled",
          type: "info"
        });
      })
      .catch(() => {
        setAlert({
          open: false,
          message: "we ran in to an error disabling the grace period",
          type: "error"
        });
      });
  };

  const activateEditing = () => {
    const updated = { ...gracePeriodState, editing: true };
    setGracePeriodState(updated);
  };

  const cancelEditing = () => {
    const updated = { ...gracePeriodState, editing: false };
    setGracePeriodState(updated);
  };

  React.useEffect(() => {
    getSettingByName(facilityID, "GracePeriod")
      .then(result => {
        if (!result.data?.GracePeriod) {
          return;
        } else {
          setGracePeriodState({
            active: true,
            editing: false,
            amount: result.data.GracePeriod,
            hasStoredPeriod: true
          });
        }
      })
      .catch(() => {
        setGracePeriodState(null);
      });
  }, [getSettingByName, facilityID]);

  return (
    <>
      <Box
        className={isLargeScreen ? classes.container : classes.smallContainer}
      >
        {gracePeriodState && gracePeriodState.active && (
          <>
            {gracePeriodState.editing ? (
              <Formik
                validate={validate}
                validateOnChange
                enableReinitialize
                initialValues={{
                  time: gracePeriodState ? gracePeriodState.amount : ""
                }}
                onSubmit={async value => {
                  await handleSubmit(value);
                }}
              >
                {({ submitForm, errors }) => (
                  <Form>
                    <Box className={classes.form}>
                      {errors && (
                        <Typography
                          className={classes.errorText}
                          component="h6"
                        >
                          {errors.time}
                        </Typography>
                      )}
                      <Field
                        component={TextField}
                        name="time"
                        value={gracePeriodState.amount || null}
                        className={classes.field}
                        variant="outlined"
                        autoFocus={true}
                        size="small"
                        label="minutes"
                        type="number"
                      />
                      <IconButton
                        name="submit button"
                        className={classes.noMargin}
                        onClick={submitForm}
                      >
                        <CheckIcon />
                      </IconButton>
                      <IconButton
                        name="cancel button"
                        className={classes.noMargin}
                        onClick={cancelEditing}
                      >
                        <CloseIcon />
                      </IconButton>
                    </Box>
                  </Form>
                )}
              </Formik>
            ) : (
              <Box
                className={isLargeScreen ? classes.inline : classes.inlineSmall}
              >
                <>
                  {gracePeriodState ? (
                    <Typography component="h6">
                      {gracePeriodState.amount} Minutes
                    </Typography>
                  ) : (
                    <Typography component="h6">Unset</Typography>
                  )}
                  {settingsWrite && (
                    <IconButton
                      className={classes.noMargin}
                      onClick={activateEditing}
                    >
                      <EditIcon />
                    </IconButton>
                  )}
                </>
              </Box>
            )}
          </>
        )}
        <FormGroup>
          <FormControlLabel
            control={
              <Switch
                color="primary"
                disabled={!settingsWrite}
                checked={(gracePeriodState && gracePeriodState.active) || false}
              />
            }
            label="Grace Period"
            labelPlacement="start"
            onClick={handleToggle}
            disabled={!settingsWrite}
          />
        </FormGroup>
        {alert && (
          <Alert
            open={alert.open}
            type={alert.type}
            onClose={() => setAlert(null)}
          >
            {alert.message}
          </Alert>
        )}
      </Box>
    </>
  );
};

export default GracePeriodForm;
