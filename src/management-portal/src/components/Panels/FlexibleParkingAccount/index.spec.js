import React from "react";
import { shallow } from "enzyme";
import { render, waitFor, screen, act } from "@testing-library/react";
import ContractService from "../../../services/ContractService";
import { useSnackbar } from "notistack";
import FlexibleParkingAccountPanel from ".";
import { useMediaQuery } from "@material-ui/core";
import { useSelector } from "react-redux";

jest.mock("notistack");
jest.mock("react-router");
jest.mock("../../../services/ContractService", () => jest.fn());
jest.mock("@material-ui/core", () => ({
  ...jest.requireActual("@material-ui/core"),
  useMediaQuery: jest.fn(),
}));
jest.mock("../../Forms/ContractDetails/FlexibleParkingAccount", () => {
  return {
    __esModule: true,
    default: function FlexibleParkingAccountForm() {
      return <div />;
    },
  };
});
jest.mock("../../Buttons/EditBalance", () => {
  return {
    __esModule: true,
    default: function EditBalanceButton() {
      return <div />;
    },
  };
});
jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useSelector: jest.fn()
}));
jest.mock("react-router-dom", () => ({
  useLocation: () => ({ location: 123 })
}));

const mockAccessHolderResponse = {
  contractID: "test-contract-id",
};

const mockFundsResponse = {
  balance: 100,
};
const mockFaciltyID = "1111";
const mockFacilityGroupID = "9999";
const mockSelectedFacilityID_1 = mockFaciltyID;
const defaultStoreState = {
  entities: {
    ContextID: mockFaciltyID,
  },
  entityScope: {
    facilityGroupId : mockFacilityGroupID,
    selected : [
      {
        id : mockSelectedFacilityID_1,
        name : "test facility"
      }
    ]
  }
};

describe("FlexibleParkingAccountPanel", () => {
  let wrapper;
  useMediaQuery.mockReturnValue(true);
  beforeEach(() => {
    ContractService.prototype.getAccessHolder = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({ status: 200, data: mockAccessHolderResponse })
      );

    ContractService.prototype.getFundBalance = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({ status: 200, data: mockFundsResponse })
      );

    useSnackbar.mockReturnValue({
      enqueueSnackbar: jest.fn(),
    });

    useSelector.mockImplementation((callback) => callback(defaultStoreState));

    wrapper = shallow(<FlexibleParkingAccountPanel />);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should display $0.00 balance when no record available", () => {
    render(<FlexibleParkingAccountPanel />);

    expect(screen.getByText(/balance \$0.00/i)).toBeInTheDocument();
  });

  it("should display accurate balance when record available", async () => {
    await act(async () => {
      render(
        <FlexibleParkingAccountPanel
          accessHolderID="test"
          facilityID="test"
          viewPermission={true}
        />
      );
    });

    expect(screen.getByText(/balance \$100.00/i)).toBeInTheDocument();
  });

  it("should not fetch balance or contract details if view permission false", async () => {
    var getHolderSpy = jest.fn();
    var getFundSpy = jest.fn();
    ContractService.prototype.getAccessHolder = getHolderSpy;
    ContractService.prototype.getFundBalance = getFundSpy;
    await act(async () => {
      render(
        <FlexibleParkingAccountPanel
          accessHolderID="test"
          facilityID="test"
          viewPermission={false}
          contractId="test"
        />
      );
    });

    await waitFor(() => expect(getHolderSpy).not.toHaveBeenCalled());
    await waitFor(() => expect(getFundSpy).not.toHaveBeenCalled());
  });

  it("should fetch balance and contract details if view permission true", async () => {
    var getHolderSpy = jest.fn();
    var getFundSpy = jest.fn();
    ContractService.prototype.getAccessHolder = getHolderSpy;
    ContractService.prototype.getFundBalance = getFundSpy;
    await act(async () => {
      render(
        <FlexibleParkingAccountPanel
          accessHolderID="test"
          facilityID="test"
          contractId="test"
          viewPermission={true}
        />
      );
    });

    await waitFor(() => expect(getHolderSpy).toHaveBeenCalled());
    await waitFor(() => expect(getFundSpy).toHaveBeenCalled());
  });

  it("should fetch balance with FG id if present", async () => {
    var getFundSpy = jest.fn();
    ContractService.prototype.getFundBalance = getFundSpy;
    await act(async () => {
      render(
        <FlexibleParkingAccountPanel
          accessHolderID="test"
          facilityID="test"
          contractId="test"
          viewPermission={true}
        />
      );
    });

    await waitFor(() => expect(getFundSpy).toHaveBeenCalledWith(mockFacilityGroupID, expect.anything()));
  });
});

describe("FlexibleParkingAccountPanel - When payment method is Credit Card On File", () => {
  let wrapper;
  useMediaQuery.mockReturnValue(true);
  beforeEach(() => {
    ContractService.prototype.getAccessHolder = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({ status: 200, data: mockAccessHolderResponse })
      );

    ContractService.prototype.getFundBalance = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({ status: 200, data: mockFundsResponse })
      );
    ContractService.prototype.getContractDetails = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({ status: 200, data: {payload: { paymentMethod: 1 }} })
      );  

    useSnackbar.mockReturnValue({
      enqueueSnackbar: jest.fn(),
    });

    useSelector.mockImplementation((callback) => callback(defaultStoreState));

    wrapper = shallow(<FlexibleParkingAccountPanel />);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should not display balance", async () => {
    await act(async () => {
      render(
        <FlexibleParkingAccountPanel
          accessHolderID="test"
          facilityID="test"
          viewPermission={false}
          contractId="test"
        />
      );
    })
  
    await waitFor(() => {
      expect(screen.queryByText("balance")).not.toBeInTheDocument();
      expect(screen.queryByText("Edit Balance")).not.toBeInTheDocument();
    });  
  });
})
