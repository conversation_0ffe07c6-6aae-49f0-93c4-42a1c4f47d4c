import React from "react";
import { waitFor } from "@testing-library/react";
import QueueDetail from "./QueueDetail";
import useHasPermissions from "../../../hooks/useHasPermissions";
import { renderWithRedux } from "../../../../TestUtils/index.js";
import { useMediaQuery } from "@material-ui/core";
import useHubContext from "../../../hooks/useHubContext";
import { useEnqueueSnackbar } from "../../../hooks/useEnqueueSnackbar";
import { useConfirmationDialog } from "../../../hooks/useConfirmationDialog";
import { createMuiTheme } from "@material-ui/core";
import { ThemeProvider } from "@material-ui/core/styles";
import useCallCenterActions from "../../../hooks/useCallCenterActions";
import useCurrentUser from "../../../hooks/useCurrentUser";
import { mockFlags, resetLDMocks } from "jest-launchdarkly-mock";
import { ListeningProvider } from "../../../hooks/useDeviceListener/useDeviceListenerContext";
import useCallCenterTransactionManager from "../Hooks/useCallCenterTransactionManager";

const MockTheme = ({ children }) => {
  const theme = createMuiTheme({
    palette: {
      amano: {
        closed: {},
        active: {},
        warnings: {},
      },
    },
  });
  return React.createElement(ThemeProvider, { theme: theme }, children);
};

jest.mock("../../../hooks/useCurrentUser");
jest.mock("../../../hooks/useCallCenterActions");
jest.mock("../Hooks/useCallCenterTransactionManager");
jest.mock("../../../hooks/useHasPermissions");
jest.mock("@material-ui/core", () => ({
  ...jest.requireActual("@material-ui/core"),
  useMediaQuery: jest.fn(),
}));
jest.mock("../../../hooks/useHubContext");
jest.mock("../../../hooks/useEnqueueSnackbar");
jest.mock("../../../hooks/useConfirmationDialog");

const mockState = {
  entities: {
    ContextID: "facility",
    Context: {
      name: "facility name"
    },
    EntityList: {
      typeid: 3,
      entityid: "2222",
      parententityid: "5555",
      name: "South Exit",
      settings: [
        {
          name: "devicemode",
          value: "Exit",
          friendly: "Device Mode",
        },
      ],
      state: {
        ActiveTransaction: {
          Ticket: {
            LostTicket: false,
            TicketID: "abcd",
          },
        },
      },
    },
  },
  callcenter: {
    Queue: [
      {
        callID: 1,
        deviceID: "2222",
        deviceName: "South Exit",
      },
      {
        callID: 2,
        deviceID: "3333",
        deviceName: "North Exit",
        parentEntityID: "1818181",
      },
    ],
  },
  user: {
    EmailAddress: "<EMAIL>",
    UserID: "someuser",
    Username: "name",
  },
};

describe("QueueDetail tests", () => {
  describe("Render tests", () => {
    beforeEach(() => {
      resetLDMocks();
      mockFlags({ callCenterSearchV2: false });
      const mockDeviceDetails = jest.fn();
      useCallCenterActions.mockReturnValue({
        getDeviceDetails: mockDeviceDetails,
        isStateLoaded: jest.fn().mockReturnValue(true)
      });
      useCallCenterTransactionManager.mockReturnValue({
        isStateLoaded: jest.fn().mockReturnValue(true)
      });
      useCurrentUser.mockReturnValue({});
      useHasPermissions.mockReturnValue({
        hasPermissions: jest.fn().mockReturnValue(true),
      });
      useMediaQuery.mockReturnValue(true);
      useHubContext.mockReturnValue({
        portalHub: {
          subscribe: jest.fn(),
          unsubscribe: jest.fn(),
          invoke: jest.fn(),
        },
        Connected: true,
      });
      useEnqueueSnackbar.mockReturnValue(jest.fn());
      useConfirmationDialog.mockReturnValue({
        openDialog: jest.fn(),
        openTextConfirmationDialog: jest.fn(),
        reasonConfirmation: jest.fn(),
      });
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it("should render device information when device exists in store", async () => {
      useCallCenterActions.mockReturnValue({ isStateLoaded: jest.fn().mockReturnValue(true) });
      useCallCenterTransactionManager.mockReturnValue({ isStateLoaded: jest.fn().mockReturnValue(true) });


      const { findByText } = renderWithRedux(
        <MockTheme>
          <ListeningProvider>
          <QueueDetail callID={1} onCompleted={jest.fn()} />
          </ListeningProvider>
        </MockTheme>,
        mockState
      );
      expect(await findByText("South Exit")).toBeVisible();
    });

    it("should not render device information when device doesn't exist in store", async() => {
      useCallCenterActions.mockReturnValue({ isStateLoaded: jest.fn().mockReturnValue(true) });
      useCallCenterTransactionManager.mockReturnValue({ isStateLoaded: jest.fn().mockReturnValue(true) });
      const { queryByText } = renderWithRedux(
        <MockTheme>
          <ListeningProvider>
          <QueueDetail callID={2} onCompleted={jest.fn()} />
          </ListeningProvider>        
        </MockTheme>,
        mockState
      );
      await waitFor(() => {
        expect(queryByText("North Exit")).not.toBeInTheDocument();
      })
    });

    it("should attempt to fetch device details on render", async() => {
      const spy = jest.fn();
      useCallCenterActions.mockReturnValue({
        getDeviceDetails: spy,
        isStateLoaded: jest.fn().mockReturnValue(true)
      });

      // Mock the hook that will trigger getDeviceDetails
      useCallCenterTransactionManager.mockImplementation(({ entityID, callID }) => {
        // Simulate the effect that calls getDeviceDetails
        React.useEffect(() => {
          if (entityID && callID) {
            const queueItem = mockState.callcenter.Queue.find(item => item.callID === callID);
            if (queueItem) {
              spy(entityID, queueItem.parentEntityID, "87777");
            }
          }
        }, [entityID, callID]);

        return { isStateLoaded: jest.fn().mockReturnValue(true) };
      });
      useCurrentUser.mockReturnValue({ UserID: "87777" });
      renderWithRedux(
        <MockTheme>
          <ListeningProvider>
          <QueueDetail callID={2} onCompleted={jest.fn()} />
          </ListeningProvider>        
        </MockTheme>,
        mockState
      );

      await waitFor(() => {
        expect(spy).toHaveBeenCalledWith("3333", "1818181", "87777");
      })
    });
  });
});
